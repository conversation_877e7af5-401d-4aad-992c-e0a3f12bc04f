<svg viewBox="0 0 520 257" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="520" height="39" fill="var(--vscode-quickInput-background)"/>
<g clip-path="url(#clip0)">
<rect width="115.654" height="39.2998" fill="var(--vscode-tab-activeBackground)"/>
<rect x="13.4741" y="15.7197" width="88.7052" height="7.85995" rx="3.92998" fill="var(--vscode-foreground)" fill-opacity="0.25"/>
</g>
<g filter="url(#filter0_d)">
<rect width="455" height="208" transform="translate(32 32)" fill="var(--vscode-quickInput-background)"/>
<rect width="414" height="28" transform="translate(53 52)" fill="var(--vscode-input-background)"/>
<path d="M59.7173 68.5562C59.8188 70.1812 61.1836 71.2222 63.2148 71.2222C65.3857 71.2222 66.7441 70.1304 66.7441 68.3975C66.7441 67.0264 65.9697 66.271 64.0908 65.8203L63.0815 65.5728C61.8882 65.2808 61.4058 64.8999 61.4058 64.2334C61.4058 63.3765 62.1548 62.8242 63.2783 62.8242C64.3447 62.8242 65.0811 63.3574 65.2144 64.2271H66.5981C66.5156 62.6973 65.1572 61.6182 63.2974 61.6182C61.2979 61.6182 59.9648 62.6973 59.9648 64.3096C59.9648 65.6426 60.7202 66.4297 62.377 66.8296L63.5576 67.1152C64.77 67.4072 65.3032 67.8516 65.3032 68.5688C65.3032 69.4067 64.4653 70.0098 63.3228 70.0098C62.0977 70.0098 61.2471 69.4448 61.1265 68.5562H59.7173ZM72.9902 69.1528C72.7363 69.7114 72.1714 70.0161 71.3462 70.0161C70.2544 70.0161 69.5498 69.229 69.5054 67.9785V67.915H74.3867V67.4453C74.3867 65.2871 73.2251 63.9731 71.3018 63.9731C69.353 63.9731 68.1152 65.376 68.1152 67.5659C68.1152 69.7686 69.3276 71.1333 71.3081 71.1333C72.8887 71.1333 74.0059 70.3716 74.3042 69.1528H72.9902ZM71.2954 65.084C72.3047 65.084 72.9648 65.814 72.9966 66.9312H69.5054C69.5815 65.8203 70.2861 65.084 71.2954 65.084ZM77.9351 71.1143C78.8428 71.1143 79.5981 70.7207 80.0107 70.0225H80.1187V71H81.4326V66.2837C81.4326 64.8364 80.4551 63.9731 78.7222 63.9731C77.1543 63.9731 76.0371 64.7285 75.8975 65.8901H77.2178C77.3701 65.3887 77.897 65.103 78.6587 65.103C79.5918 65.103 80.0742 65.5283 80.0742 66.2837V66.8867L78.2017 67.001C76.5576 67.1025 75.6309 67.8198 75.6309 69.0576C75.6309 70.3145 76.6021 71.1143 77.9351 71.1143ZM78.2842 70.0161C77.5415 70.0161 77.002 69.6416 77.002 69.0005C77.002 68.3721 77.4336 68.0356 78.3857 67.9722L80.0742 67.8579V68.4546C80.0742 69.3433 79.3125 70.0161 78.2842 70.0161ZM83.375 71H84.7397V66.8804C84.7397 65.9092 85.4697 65.2427 86.5044 65.2427C86.7456 65.2427 87.1519 65.2871 87.2661 65.3188V64.0366C87.1201 64.0049 86.8599 63.9858 86.6567 63.9858C85.7554 63.9858 84.9873 64.4746 84.7905 65.1538H84.689V64.1064H83.375V71ZM94.2549 66.3218C94.0771 64.9824 93.0298 63.9731 91.2969 63.9731C89.2847 63.9731 88.0596 65.3379 88.0596 67.5278C88.0596 69.7622 89.291 71.1333 91.3032 71.1333C93.0107 71.1333 94.0771 70.1748 94.2549 68.8164H92.9282C92.7505 69.5654 92.1602 69.9717 91.2969 69.9717C90.1606 69.9717 89.4561 69.0513 89.4561 67.5278C89.4561 66.0361 90.1543 65.1348 91.2969 65.1348C92.2109 65.1348 92.7695 65.6489 92.9282 66.3218H94.2549ZM95.8799 71H97.2446V66.9629C97.2446 65.8838 97.8667 65.1665 98.9839 65.1665C99.9487 65.1665 100.463 65.7314 100.463 66.8677V71H101.828V66.5439C101.828 64.9126 100.92 63.9795 99.4473 63.9795C98.4062 63.9795 97.6763 64.4365 97.3398 65.2046H97.2319V61.4023H95.8799V71ZM108.842 67.0137H108.734V61.4023H107.369V71H108.734V68.4927L109.337 67.915L111.711 71H113.387L110.353 67.0581L113.196 64.1064H111.584L108.842 67.0137ZM118.82 69.1528C118.566 69.7114 118.001 70.0161 117.176 70.0161C116.084 70.0161 115.38 69.229 115.335 67.9785V67.915H120.217V67.4453C120.217 65.2871 119.055 63.9731 117.132 63.9731C115.183 63.9731 113.945 65.376 113.945 67.5659C113.945 69.7686 115.158 71.1333 117.138 71.1333C118.719 71.1333 119.836 70.3716 120.134 69.1528H118.82ZM117.125 65.084C118.135 65.084 118.795 65.814 118.827 66.9312H115.335C115.412 65.8203 116.116 65.084 117.125 65.084ZM122.388 73.501C123.797 73.501 124.451 72.9741 125.041 71.3174L127.618 64.1064H126.171L124.444 69.6289H124.336L122.604 64.1064H121.118L123.619 71.019L123.518 71.3745C123.283 72.1045 122.915 72.3774 122.267 72.3774C122.14 72.3774 121.931 72.3711 121.823 72.3521V73.4692C121.95 73.4883 122.273 73.501 122.388 73.501ZM132.677 71.1143C134.423 71.1143 135.534 69.7241 135.534 67.5532C135.534 65.3633 134.429 63.9858 132.677 63.9858C131.731 63.9858 130.919 64.4492 130.551 65.1982H130.443V61.4023H129.078V71H130.392V69.9082H130.494C130.906 70.6699 131.712 71.1143 132.677 71.1143ZM132.284 65.1538C133.439 65.1538 134.131 66.0615 134.131 67.5532C134.131 69.0449 133.439 69.9463 132.284 69.9463C131.135 69.9463 130.424 69.0322 130.417 67.5532C130.424 66.0742 131.141 65.1538 132.284 65.1538ZM137.882 62.875C138.358 62.875 138.746 62.4878 138.746 62.0181C138.746 61.542 138.358 61.1548 137.882 61.1548C137.406 61.1548 137.019 61.542 137.019 62.0181C137.019 62.4878 137.406 62.875 137.882 62.875ZM137.203 71H138.562V64.1064H137.203V71ZM140.567 71H141.932V66.9565C141.932 65.8457 142.573 65.1602 143.583 65.1602C144.592 65.1602 145.074 65.7188 145.074 66.8613V71H146.439V66.5376C146.439 64.8936 145.588 63.9731 144.046 63.9731C143.005 63.9731 142.319 64.4365 141.983 65.1982H141.881V64.1064H140.567V71ZM150.889 71.1143C151.841 71.1143 152.647 70.6636 153.06 69.9082H153.167V71H154.475V61.4023H153.11V65.1982H153.009C152.634 64.4429 151.834 63.9858 150.889 63.9858C149.143 63.9858 148.02 65.376 148.02 67.5469C148.02 69.7305 149.13 71.1143 150.889 71.1143ZM151.276 65.1538C152.418 65.1538 153.136 66.0806 153.136 67.5532C153.136 69.0386 152.425 69.9463 151.276 69.9463C150.121 69.9463 149.429 69.0513 149.429 67.5532C149.429 66.0615 150.127 65.1538 151.276 65.1538ZM157.23 62.875C157.706 62.875 158.093 62.4878 158.093 62.0181C158.093 61.542 157.706 61.1548 157.23 61.1548C156.754 61.1548 156.367 61.542 156.367 62.0181C156.367 62.4878 156.754 62.875 157.23 62.875ZM156.551 71H157.909V64.1064H156.551V71ZM159.915 71H161.28V66.9565C161.28 65.8457 161.921 65.1602 162.93 65.1602C163.939 65.1602 164.422 65.7188 164.422 66.8613V71H165.787V66.5376C165.787 64.8936 164.936 63.9731 163.394 63.9731C162.353 63.9731 161.667 64.4365 161.331 65.1982H161.229V64.1064H159.915V71ZM170.624 73.6343C172.579 73.6343 173.816 72.6377 173.816 71.0698V64.1064H172.509V65.2046H172.401C172.014 64.4556 171.182 63.9858 170.23 63.9858C168.465 63.9858 167.361 65.376 167.361 67.4644C167.361 69.5337 168.453 70.8984 170.217 70.8984C171.169 70.8984 171.931 70.4858 172.35 69.7368H172.452V71.0635C172.452 71.9966 171.779 72.5552 170.643 72.5552C169.722 72.5552 169.145 72.2251 169.03 71.7046H167.634C167.78 72.8662 168.897 73.6343 170.624 73.6343ZM170.604 69.7686C169.43 69.7686 168.77 68.8735 168.77 67.4644C168.77 66.0552 169.43 65.1538 170.604 65.1538C171.766 65.1538 172.477 66.0552 172.477 67.4644C172.477 68.8735 171.772 69.7686 170.604 69.7686ZM175.638 66.0361C175.638 67.0581 176.248 67.6421 177.562 67.9468L178.768 68.2324C179.459 68.3975 179.783 68.6768 179.783 69.1084C179.783 69.6733 179.18 70.0669 178.33 70.0669C177.504 70.0669 176.997 69.7305 176.825 69.1909H175.46C175.581 70.3906 176.654 71.1333 178.298 71.1333C179.948 71.1333 181.161 70.2637 181.161 68.9814C181.161 67.9785 180.545 67.4136 179.231 67.1089L178.082 66.8423C177.333 66.6646 176.984 66.3979 176.984 65.9727C176.984 65.4141 177.562 65.0396 178.323 65.0396C179.098 65.0396 179.593 65.376 179.72 65.8901H181.027C180.894 64.6968 179.878 63.9731 178.323 63.9731C176.762 63.9731 175.638 64.8364 175.638 66.0361Z" fill="var(--vscode-input-foreground)"/>
<path d="M69.002 101.235C71.2109 101.235 72.8232 99.8955 72.9819 97.9404H71.1157C70.9316 98.9497 70.1064 99.5972 69.0083 99.5972C67.561 99.5972 66.666 98.3784 66.666 96.417C66.666 94.4556 67.561 93.2368 69.002 93.2368C70.0938 93.2368 70.9253 93.9351 71.1094 94.9951H72.9756C72.8359 93.0273 71.1729 91.6055 69.002 91.6055C66.3423 91.6055 64.7046 93.4399 64.7046 96.417C64.7046 99.394 66.3486 101.235 69.002 101.235ZM77.6665 101.146C79.8184 101.146 81.1133 99.7876 81.1133 97.5024C81.1133 95.2363 79.7993 93.8589 77.6665 93.8589C75.5337 93.8589 74.2197 95.2427 74.2197 97.5024C74.2197 99.7812 75.5146 101.146 77.6665 101.146ZM77.6665 99.686C76.6763 99.686 76.1177 98.8862 76.1177 97.5024C76.1177 96.1313 76.6826 95.3252 77.6665 95.3252C78.644 95.3252 79.2153 96.1313 79.2153 97.5024C79.2153 98.8862 78.6504 99.686 77.6665 99.686ZM82.624 101H84.4712V96.7852C84.4712 95.9854 84.9854 95.4014 85.709 95.4014C86.4326 95.4014 86.8643 95.833 86.8643 96.582V101H88.6416V96.7026C88.6416 95.9473 89.124 95.4014 89.873 95.4014C90.6538 95.4014 91.041 95.8203 91.041 96.6646V101H92.8882V96.2012C92.8882 94.7603 92.0186 93.8589 90.6221 93.8589C89.6445 93.8589 88.8384 94.373 88.5146 95.1475H88.4004C88.1211 94.3477 87.4482 93.8589 86.4644 93.8589C85.5439 93.8589 84.814 94.3477 84.5283 95.1475H84.4141V94.0112H82.624V101ZM94.6846 101H96.5317V96.7852C96.5317 95.9854 97.0459 95.4014 97.7695 95.4014C98.4932 95.4014 98.9248 95.833 98.9248 96.582V101H100.702V96.7026C100.702 95.9473 101.185 95.4014 101.934 95.4014C102.714 95.4014 103.102 95.8203 103.102 96.6646V101H104.949V96.2012C104.949 94.7603 104.079 93.8589 102.683 93.8589C101.705 93.8589 100.899 94.373 100.575 95.1475H100.461C100.182 94.3477 99.5088 93.8589 98.5249 93.8589C97.6045 93.8589 96.8745 94.3477 96.5889 95.1475H96.4746V94.0112H94.6846V101ZM108.662 101.108C109.57 101.108 110.332 100.721 110.693 100.073H110.808V101H112.598V96.2266C112.598 94.7349 111.55 93.8589 109.697 93.8589C107.938 93.8589 106.751 94.665 106.631 95.9473H108.332C108.484 95.5093 108.929 95.2744 109.595 95.2744C110.357 95.2744 110.776 95.6235 110.776 96.2266V96.8042L109.081 96.9058C107.342 97.0073 106.371 97.7437 106.371 99.0068C106.371 100.283 107.316 101.108 108.662 101.108ZM109.271 99.7559C108.63 99.7559 108.18 99.4321 108.18 98.8926C108.18 98.3784 108.567 98.0801 109.36 98.0293L110.776 97.9341V98.4546C110.776 99.1909 110.128 99.7559 109.271 99.7559ZM114.413 101H116.26V96.9565C116.26 96.0107 116.787 95.4014 117.644 95.4014C118.52 95.4014 118.933 95.9155 118.933 96.8994V101H120.78V96.4805C120.78 94.811 119.942 93.8589 118.387 93.8589C117.352 93.8589 116.641 94.3477 116.317 95.1348H116.203V94.0112H114.413V101ZM125.083 101.108C126.055 101.108 126.848 100.657 127.223 99.9019H127.337V101H129.127V91.3389H127.28V95.1284H127.172C126.81 94.354 126.029 93.897 125.083 93.897C123.325 93.897 122.233 95.2744 122.233 97.4961C122.233 99.7241 123.319 101.108 125.083 101.108ZM125.712 95.395C126.696 95.395 127.299 96.2012 127.299 97.5088C127.299 98.8164 126.702 99.6162 125.712 99.6162C124.722 99.6162 124.131 98.8228 124.131 97.5024C124.131 96.1885 124.728 95.395 125.712 95.395Z" fill="var(--vscode-tab-activeForeground)"/>
<path d="M317.028 101V98.2007L317.936 97.1089L320.595 101H322.893L319.319 95.8521L322.658 91.8403H320.525L317.142 95.9536H317.028V91.8403H315.111V101H317.028ZM328.136 99.0259C327.939 99.502 327.438 99.7686 326.708 99.7686C325.743 99.7686 325.134 99.1021 325.108 98.0356V97.9404H329.914V97.3882C329.914 95.1792 328.695 93.8589 326.632 93.8589C324.55 93.8589 323.268 95.2681 323.268 97.5405C323.268 99.8003 324.524 101.146 326.657 101.146C328.371 101.146 329.583 100.327 329.856 99.0259H328.136ZM326.638 95.2363C327.501 95.2363 328.06 95.833 328.098 96.7915H325.115C325.178 95.8521 325.781 95.2363 326.638 95.2363ZM332.11 103.539C333.855 103.539 334.712 102.917 335.309 101.133L337.721 94.0112H335.766L334.293 99.3433H334.179L332.707 94.0112H330.669L333.125 101.038L333.062 101.305C332.91 101.87 332.516 102.098 331.824 102.098C331.748 102.098 331.469 102.092 331.405 102.079V103.52C331.481 103.533 332.04 103.539 332.11 103.539ZM343.136 101.108C344.894 101.108 345.973 99.7432 345.973 97.5024C345.973 95.249 344.907 93.897 343.136 93.897C342.177 93.897 341.39 94.3604 341.035 95.1284H340.92V91.3389H339.073V101H340.863V99.9019H340.978C341.352 100.664 342.146 101.108 343.136 101.108ZM342.495 95.395C343.472 95.395 344.075 96.2012 344.075 97.5024C344.075 98.8164 343.479 99.6162 342.495 99.6162C341.517 99.6162 340.908 98.8101 340.901 97.5088C340.908 96.2075 341.523 95.395 342.495 95.395ZM348.417 92.9385C348.988 92.9385 349.458 92.4814 349.458 91.9102C349.458 91.3325 348.988 90.8755 348.417 90.8755C347.846 90.8755 347.37 91.3325 347.37 91.9102C347.37 92.4814 347.846 92.9385 348.417 92.9385ZM347.49 101H349.337V94.0112H347.49V101ZM351.21 101H353.057V96.9565C353.057 96.0107 353.584 95.4014 354.441 95.4014C355.317 95.4014 355.729 95.9155 355.729 96.8994V101H357.577V96.4805C357.577 94.811 356.739 93.8589 355.184 93.8589C354.149 93.8589 353.438 94.3477 353.114 95.1348H353V94.0112H351.21V101ZM361.88 101.108C362.852 101.108 363.645 100.657 364.02 99.9019H364.134V101H365.924V91.3389H364.077V95.1284H363.969C363.607 94.354 362.826 93.897 361.88 93.897C360.122 93.897 359.03 95.2744 359.03 97.4961C359.03 99.7241 360.116 101.108 361.88 101.108ZM362.509 95.395C363.493 95.395 364.096 96.2012 364.096 97.5088C364.096 98.8164 363.499 99.6162 362.509 99.6162C361.519 99.6162 360.928 98.8228 360.928 97.5024C360.928 96.1885 361.525 95.395 362.509 95.395ZM368.78 92.9385C369.352 92.9385 369.821 92.4814 369.821 91.9102C369.821 91.3325 369.352 90.8755 368.78 90.8755C368.209 90.8755 367.733 91.3325 367.733 91.9102C367.733 92.4814 368.209 92.9385 368.78 92.9385ZM367.854 101H369.701V94.0112H367.854V101ZM371.573 101H373.42V96.9565C373.42 96.0107 373.947 95.4014 374.804 95.4014C375.68 95.4014 376.093 95.9155 376.093 96.8994V101H377.94V96.4805C377.94 94.811 377.102 93.8589 375.547 93.8589C374.512 93.8589 373.801 94.3477 373.478 95.1348H373.363V94.0112H371.573V101ZM382.821 103.647C384.935 103.647 386.274 102.619 386.274 100.987V94.0112H384.484V95.1284H384.37C383.996 94.3667 383.189 93.897 382.244 93.897C380.466 93.897 379.375 95.2808 379.375 97.4453C379.375 99.54 380.46 100.898 382.206 100.898C383.189 100.898 383.926 100.499 384.313 99.7622H384.427V101.063C384.427 101.87 383.85 102.339 382.859 102.339C382.06 102.339 381.539 102.06 381.444 101.616H379.616C379.73 102.841 380.961 103.647 382.821 103.647ZM382.847 99.4829C381.844 99.4829 381.272 98.6958 381.272 97.439C381.272 96.1821 381.85 95.395 382.847 95.395C383.843 95.395 384.446 96.1821 384.446 97.439C384.446 98.6958 383.85 99.4829 382.847 99.4829Z" fill="var(--vscode-tab-activeForeground)"/>
<path d="M66.5708 125V121.166H70.5063V119.966H66.5708V117.065H70.8682V115.84H65.1489V125H66.5708ZM73.2612 116.875C73.7373 116.875 74.1245 116.488 74.1245 116.018C74.1245 115.542 73.7373 115.155 73.2612 115.155C72.7852 115.155 72.3979 115.542 72.3979 116.018C72.3979 116.488 72.7852 116.875 73.2612 116.875ZM72.582 125H73.9404V118.106H72.582V125ZM76.0098 125H77.3745V115.402H76.0098V125ZM83.957 123.153C83.7031 123.711 83.1382 124.016 82.313 124.016C81.2212 124.016 80.5166 123.229 80.4722 121.979V121.915H85.3535V121.445C85.3535 119.287 84.1919 117.973 82.2686 117.973C80.3198 117.973 79.082 119.376 79.082 121.566C79.082 123.769 80.2944 125.133 82.2749 125.133C83.8555 125.133 84.9727 124.372 85.271 123.153H83.957ZM82.2622 119.084C83.2715 119.084 83.9316 119.814 83.9634 120.931H80.4722C80.5483 119.82 81.2529 119.084 82.2622 119.084ZM88.0513 120.328C88.5908 120.328 88.9844 119.928 88.9844 119.414C88.9844 118.9 88.5908 118.5 88.0513 118.5C87.5181 118.5 87.1182 118.9 87.1182 119.414C87.1182 119.928 87.5181 120.328 88.0513 120.328ZM88.0513 125.095C88.5908 125.095 88.9844 124.695 88.9844 124.181C88.9844 123.667 88.5908 123.267 88.0513 123.267C87.5181 123.267 87.1182 123.667 87.1182 124.181C87.1182 124.695 87.5181 125.095 88.0513 125.095ZM96.189 125V118.348H96.2905L100.994 125H102.276V115.84H100.893V122.505H100.791L96.0874 115.84H94.8052V125H96.189ZM108.954 123.153C108.7 123.711 108.135 124.016 107.31 124.016C106.218 124.016 105.514 123.229 105.469 121.979V121.915H110.351V121.445C110.351 119.287 109.189 117.973 107.266 117.973C105.317 117.973 104.079 119.376 104.079 121.566C104.079 123.769 105.292 125.133 107.272 125.133C108.853 125.133 109.97 124.372 110.268 123.153H108.954ZM107.259 119.084C108.269 119.084 108.929 119.814 108.96 120.931H105.469C105.545 119.82 106.25 119.084 107.259 119.084ZM120.938 118.106H119.574L118.342 123.426H118.234L116.812 118.106H115.505L114.083 123.426H113.981L112.744 118.106H111.36L113.264 125H114.667L116.089 119.858H116.197L117.625 125H119.041L120.938 118.106ZM127.47 115.84H126.048V121.845C126.048 123.826 127.464 125.222 129.755 125.222C132.06 125.222 133.469 123.826 133.469 121.845V115.84H132.047V121.731C132.047 123.039 131.215 123.959 129.755 123.959C128.302 123.959 127.47 123.039 127.47 121.731V115.84ZM135.602 125H136.966V120.957C136.966 119.846 137.607 119.16 138.617 119.16C139.626 119.16 140.108 119.719 140.108 120.861V125H141.473V120.538C141.473 118.894 140.623 117.973 139.08 117.973C138.039 117.973 137.354 118.437 137.017 119.198H136.916V118.106H135.602V125ZM143.879 116.367V118.125H142.781V119.217H143.879V123.185C143.879 124.511 144.482 125.044 145.999 125.044C146.266 125.044 146.52 125.013 146.742 124.975V123.889C146.551 123.908 146.431 123.921 146.221 123.921C145.542 123.921 145.244 123.597 145.244 122.854V119.217H146.742V118.125H145.244V116.367H143.879ZM149.052 116.875C149.528 116.875 149.916 116.488 149.916 116.018C149.916 115.542 149.528 115.155 149.052 115.155C148.576 115.155 148.189 115.542 148.189 116.018C148.189 116.488 148.576 116.875 149.052 116.875ZM148.373 125H149.731V118.106H148.373V125ZM152.22 116.367V118.125H151.122V119.217H152.22V123.185C152.22 124.511 152.823 125.044 154.34 125.044C154.606 125.044 154.86 125.013 155.083 124.975V123.889C154.892 123.908 154.771 123.921 154.562 123.921C153.883 123.921 153.584 123.597 153.584 122.854V119.217H155.083V118.125H153.584V116.367H152.22ZM156.752 125H158.117V115.402H156.752V125ZM164.699 123.153C164.445 123.711 163.88 124.016 163.055 124.016C161.963 124.016 161.259 123.229 161.214 121.979V121.915H166.096V121.445C166.096 119.287 164.934 117.973 163.011 117.973C161.062 117.973 159.824 119.376 159.824 121.566C159.824 123.769 161.037 125.133 163.017 125.133C164.598 125.133 165.715 124.372 166.013 123.153H164.699ZM163.004 119.084C164.014 119.084 164.674 119.814 164.706 120.931H161.214C161.291 119.82 161.995 119.084 163.004 119.084ZM170.26 125.114C171.212 125.114 172.018 124.664 172.431 123.908H172.539V125H173.846V115.402H172.481V119.198H172.38C172.005 118.443 171.206 117.986 170.26 117.986C168.514 117.986 167.391 119.376 167.391 121.547C167.391 123.73 168.501 125.114 170.26 125.114ZM170.647 119.154C171.79 119.154 172.507 120.081 172.507 121.553C172.507 123.039 171.796 123.946 170.647 123.946C169.492 123.946 168.8 123.051 168.8 121.553C168.8 120.062 169.498 119.154 170.647 119.154ZM181.032 125V121.166H184.967V119.966H181.032V117.065H185.329V115.84H179.61V125H181.032ZM187.722 116.875C188.198 116.875 188.585 116.488 188.585 116.018C188.585 115.542 188.198 115.155 187.722 115.155C187.246 115.155 186.859 115.542 186.859 116.018C186.859 116.488 187.246 116.875 187.722 116.875ZM187.043 125H188.401V118.106H187.043V125ZM190.471 125H191.835V115.402H190.471V125ZM198.418 123.153C198.164 123.711 197.599 124.016 196.774 124.016C195.682 124.016 194.978 123.229 194.933 121.979V121.915H199.814V121.445C199.814 119.287 198.653 117.973 196.729 117.973C194.781 117.973 193.543 119.376 193.543 121.566C193.543 123.769 194.755 125.133 196.736 125.133C198.316 125.133 199.434 124.372 199.732 123.153H198.418ZM196.723 119.084C197.732 119.084 198.393 119.814 198.424 120.931H194.933C195.009 119.82 195.714 119.084 196.723 119.084Z" fill="var(--vscode-foreground)"/>
<g filter="url(#filter1_i)">
<path d="M314 113C314 111.895 314.895 111 316 111H334C335.105 111 336 111.895 336 113V127C336 128.105 335.105 129 334 129H316C314.895 129 314 128.105 314 127V113Z" fill="var(--vscode-keybindingLabel-background)"/>
<path d="M323.154 118.85V120.695H322.352C321.396 120.701 320.617 121.451 320.617 122.412C320.617 123.373 321.391 124.152 322.352 124.152C323.307 124.152 324.086 123.373 324.086 122.412V121.621H325.908V122.412C325.908 123.373 326.688 124.152 327.643 124.152C328.604 124.152 329.383 123.373 329.383 122.412C329.383 121.451 328.604 120.701 327.643 120.695H326.846V118.85H327.643C328.604 118.85 329.383 118.094 329.383 117.133C329.383 116.172 328.604 115.393 327.643 115.393C326.688 115.393 325.908 116.172 325.908 117.133V117.93H324.086V117.133C324.086 116.172 323.307 115.393 322.352 115.393C321.391 115.393 320.617 116.172 320.617 117.133C320.617 118.094 321.396 118.85 322.352 118.85H323.154ZM322.363 117.941C321.918 117.941 321.555 117.578 321.555 117.133C321.555 116.693 321.918 116.33 322.352 116.33C322.785 116.33 323.154 116.693 323.154 117.145V117.941H322.363ZM327.631 117.941H326.846V117.145C326.846 116.693 327.209 116.33 327.643 116.33C328.082 116.33 328.439 116.693 328.439 117.133C328.439 117.578 328.082 117.941 327.631 117.941ZM324.086 120.707V118.844H325.908V120.707H324.086ZM322.363 121.604H323.154V122.395C323.154 122.846 322.785 123.209 322.352 123.209C321.918 123.209 321.555 122.852 321.555 122.406C321.555 121.967 321.918 121.604 322.363 121.604ZM327.631 121.604C328.082 121.604 328.439 121.967 328.439 122.406C328.439 122.852 328.082 123.209 327.643 123.209C327.209 123.209 326.846 122.846 326.846 122.395V121.604H327.631Z" fill="var(--vscode-foreground)"/>

</g>
<g filter="url(#filter2_i)">
<path d="M340 113C340 111.895 340.895 111 342 111H358C359.105 111 360 111.895 360 113V127C360 128.105 359.105 129 358 129H342C340.895 129 340 128.105 340 127V113Z" fill="var(--vscode-keybindingLabel-background)"/>
<path d="M347.826 124V117.859H347.92L352.262 124H353.445V115.545H352.168V121.697H352.074L347.732 115.545H346.549V124H347.826Z" fill="var(--vscode-foreground)"/>
</g>
<path d="M66.5708 149V145.166H70.5063V143.966H66.5708V141.065H70.8682V139.84H65.1489V149H66.5708ZM73.2612 140.875C73.7373 140.875 74.1245 140.488 74.1245 140.018C74.1245 139.542 73.7373 139.155 73.2612 139.155C72.7852 139.155 72.3979 139.542 72.3979 140.018C72.3979 140.488 72.7852 140.875 73.2612 140.875ZM72.582 149H73.9404V142.106H72.582V149ZM76.0098 149H77.3745V139.402H76.0098V149ZM83.957 147.153C83.7031 147.711 83.1382 148.016 82.313 148.016C81.2212 148.016 80.5166 147.229 80.4722 145.979V145.915H85.3535V145.445C85.3535 143.287 84.1919 141.973 82.2686 141.973C80.3198 141.973 79.082 143.376 79.082 145.566C79.082 147.769 80.2944 149.133 82.2749 149.133C83.8555 149.133 84.9727 148.372 85.271 147.153H83.957ZM82.2622 143.084C83.2715 143.084 83.9316 143.814 83.9634 144.931H80.4722C80.5483 143.82 81.2529 143.084 82.2622 143.084ZM88.0513 144.328C88.5908 144.328 88.9844 143.928 88.9844 143.414C88.9844 142.9 88.5908 142.5 88.0513 142.5C87.5181 142.5 87.1182 142.9 87.1182 143.414C87.1182 143.928 87.5181 144.328 88.0513 144.328ZM88.0513 149.095C88.5908 149.095 88.9844 148.695 88.9844 148.181C88.9844 147.667 88.5908 147.267 88.0513 147.267C87.5181 147.267 87.1182 147.667 87.1182 148.181C87.1182 148.695 87.5181 149.095 88.0513 149.095ZM98.709 139.618C96.0811 139.618 94.4307 141.465 94.4307 144.417C94.4307 147.362 96.043 149.222 98.709 149.222C101.356 149.222 102.981 147.356 102.981 144.417C102.981 141.472 101.343 139.618 98.709 139.618ZM98.709 140.875C100.442 140.875 101.527 142.246 101.527 144.417C101.527 146.569 100.448 147.965 98.709 147.965C96.9443 147.965 95.8843 146.569 95.8843 144.417C95.8843 142.246 96.9761 140.875 98.709 140.875ZM108.338 141.986C107.399 141.986 106.586 142.462 106.167 143.249H106.066V142.106H104.752V151.298H106.117V147.959H106.225C106.586 148.689 107.367 149.114 108.351 149.114C110.097 149.114 111.208 147.73 111.208 145.553C111.208 143.363 110.097 141.986 108.338 141.986ZM107.951 147.946C106.809 147.946 106.091 147.026 106.091 145.553C106.091 144.074 106.809 143.154 107.958 143.154C109.113 143.154 109.805 144.055 109.805 145.553C109.805 147.051 109.113 147.946 107.951 147.946ZM117.384 147.153C117.13 147.711 116.565 148.016 115.74 148.016C114.648 148.016 113.943 147.229 113.899 145.979V145.915H118.78V145.445C118.78 143.287 117.619 141.973 115.695 141.973C113.747 141.973 112.509 143.376 112.509 145.566C112.509 147.769 113.721 149.133 115.702 149.133C117.282 149.133 118.399 148.372 118.698 147.153H117.384ZM115.689 143.084C116.698 143.084 117.358 143.814 117.39 144.931H113.899C113.975 143.82 114.68 143.084 115.689 143.084ZM120.418 149H121.783V144.957C121.783 143.846 122.424 143.16 123.433 143.16C124.442 143.16 124.925 143.719 124.925 144.861V149H126.29V144.538C126.29 142.894 125.439 141.973 123.896 141.973C122.855 141.973 122.17 142.437 121.833 143.198H121.732V142.106H120.418V149ZM129.273 149.095C129.812 149.095 130.206 148.695 130.206 148.181C130.206 147.667 129.812 147.267 129.273 147.267C128.74 147.267 128.34 147.667 128.34 148.181C128.34 148.695 128.74 149.095 129.273 149.095ZM133.374 149.095C133.913 149.095 134.307 148.695 134.307 148.181C134.307 147.667 133.913 147.267 133.374 147.267C132.84 147.267 132.44 147.667 132.44 148.181C132.44 148.695 132.84 149.095 133.374 149.095ZM137.474 149.095C138.014 149.095 138.407 148.695 138.407 148.181C138.407 147.667 138.014 147.267 137.474 147.267C136.941 147.267 136.541 147.667 136.541 148.181C136.541 148.695 136.941 149.095 137.474 149.095Z" fill="var(--vscode-foreground)"/>
<g filter="url(#filter3_i)">
<path d="M314 137C314 135.895 314.895 135 316 135H334C335.105 135 336 135.895 336 137V151C336 152.105 335.105 153 334 153H316C314.895 153 314 152.105 314 151V137Z" fill="var(--vscode-keybindingLabel-background)"/>
<path d="M323.154 142.85V144.695H322.352C321.396 144.701 320.617 145.451 320.617 146.412C320.617 147.373 321.391 148.152 322.352 148.152C323.307 148.152 324.086 147.373 324.086 146.412V145.621H325.908V146.412C325.908 147.373 326.688 148.152 327.643 148.152C328.604 148.152 329.383 147.373 329.383 146.412C329.383 145.451 328.604 144.701 327.643 144.695H326.846V142.85H327.643C328.604 142.85 329.383 142.094 329.383 141.133C329.383 140.172 328.604 139.393 327.643 139.393C326.688 139.393 325.908 140.172 325.908 141.133V141.93H324.086V141.133C324.086 140.172 323.307 139.393 322.352 139.393C321.391 139.393 320.617 140.172 320.617 141.133C320.617 142.094 321.396 142.85 322.352 142.85H323.154ZM322.363 141.941C321.918 141.941 321.555 141.578 321.555 141.133C321.555 140.693 321.918 140.33 322.352 140.33C322.785 140.33 323.154 140.693 323.154 141.145V141.941H322.363ZM327.631 141.941H326.846V141.145C326.846 140.693 327.209 140.33 327.643 140.33C328.082 140.33 328.439 140.693 328.439 141.133C328.439 141.578 328.082 141.941 327.631 141.941ZM324.086 144.707V142.844H325.908V144.707H324.086ZM322.363 145.604H323.154V146.395C323.154 146.846 322.785 147.209 322.352 147.209C321.918 147.209 321.555 146.852 321.555 146.406C321.555 145.967 321.918 145.604 322.363 145.604ZM327.631 145.604C328.082 145.604 328.439 145.967 328.439 146.406C328.439 146.852 328.082 147.209 327.643 147.209C327.209 147.209 326.846 146.846 326.846 146.395V145.604H327.631Z" fill="var(--vscode-foreground)"/>
</g>
<g filter="url(#filter4_i)">
<path d="M340 137C340 135.895 340.895 135 342 135H358C359.105 135 360 135.895 360 137V151C360 152.105 359.105 153 358 153H342C340.895 153 340 152.105 340 151V137Z" fill="var(--vscode-keybindingLabel-background)"/>
<path d="M350 139.34C347.574 139.34 346.051 141.045 346.051 143.77C346.051 146.488 347.539 148.205 350 148.205C352.443 148.205 353.943 146.482 353.943 143.77C353.943 141.051 352.432 139.34 350 139.34ZM350 140.5C351.6 140.5 352.602 141.766 352.602 143.77C352.602 145.756 351.605 147.045 350 147.045C348.371 147.045 347.393 145.756 347.393 143.77C347.393 141.766 348.4 140.5 350 140.5Z" fill="var(--vscode-foreground)"/>
</g>
<path d="M66.5708 173V169.166H70.5063V167.966H66.5708V165.065H70.8682V163.84H65.1489V173H66.5708ZM73.2612 164.875C73.7373 164.875 74.1245 164.488 74.1245 164.018C74.1245 163.542 73.7373 163.155 73.2612 163.155C72.7852 163.155 72.3979 163.542 72.3979 164.018C72.3979 164.488 72.7852 164.875 73.2612 164.875ZM72.582 173H73.9404V166.106H72.582V173ZM76.0098 173H77.3745V163.402H76.0098V173ZM83.957 171.153C83.7031 171.711 83.1382 172.016 82.313 172.016C81.2212 172.016 80.5166 171.229 80.4722 169.979V169.915H85.3535V169.445C85.3535 167.287 84.1919 165.973 82.2686 165.973C80.3198 165.973 79.082 167.376 79.082 169.566C79.082 171.769 80.2944 173.133 82.2749 173.133C83.8555 173.133 84.9727 172.372 85.271 171.153H83.957ZM82.2622 167.084C83.2715 167.084 83.9316 167.814 83.9634 168.931H80.4722C80.5483 167.82 81.2529 167.084 82.2622 167.084ZM88.0513 168.328C88.5908 168.328 88.9844 167.928 88.9844 167.414C88.9844 166.9 88.5908 166.5 88.0513 166.5C87.5181 166.5 87.1182 166.9 87.1182 167.414C87.1182 167.928 87.5181 168.328 88.0513 168.328ZM88.0513 173.095C88.5908 173.095 88.9844 172.695 88.9844 172.181C88.9844 171.667 88.5908 171.267 88.0513 171.267C87.5181 171.267 87.1182 171.667 87.1182 172.181C87.1182 172.695 87.5181 173.095 88.0513 173.095ZM98.709 163.618C96.0811 163.618 94.4307 165.465 94.4307 168.417C94.4307 171.362 96.043 173.222 98.709 173.222C101.356 173.222 102.981 171.356 102.981 168.417C102.981 165.472 101.343 163.618 98.709 163.618ZM98.709 164.875C100.442 164.875 101.527 166.246 101.527 168.417C101.527 170.569 100.448 171.965 98.709 171.965C96.9443 171.965 95.8843 170.569 95.8843 168.417C95.8843 166.246 96.9761 164.875 98.709 164.875ZM108.338 165.986C107.399 165.986 106.586 166.462 106.167 167.249H106.066V166.106H104.752V175.298H106.117V171.959H106.225C106.586 172.689 107.367 173.114 108.351 173.114C110.097 173.114 111.208 171.73 111.208 169.553C111.208 167.363 110.097 165.986 108.338 165.986ZM107.951 171.946C106.809 171.946 106.091 171.026 106.091 169.553C106.091 168.074 106.809 167.154 107.958 167.154C109.113 167.154 109.805 168.055 109.805 169.553C109.805 171.051 109.113 171.946 107.951 171.946ZM117.384 171.153C117.13 171.711 116.565 172.016 115.74 172.016C114.648 172.016 113.943 171.229 113.899 169.979V169.915H118.78V169.445C118.78 167.287 117.619 165.973 115.695 165.973C113.747 165.973 112.509 167.376 112.509 169.566C112.509 171.769 113.721 173.133 115.702 173.133C117.282 173.133 118.399 172.372 118.698 171.153H117.384ZM115.689 167.084C116.698 167.084 117.358 167.814 117.39 168.931H113.899C113.975 167.82 114.68 167.084 115.689 167.084ZM120.418 173H121.783V168.957C121.783 167.846 122.424 167.16 123.433 167.16C124.442 167.16 124.925 167.719 124.925 168.861V173H126.29V168.538C126.29 166.894 125.439 165.973 123.896 165.973C122.855 165.973 122.17 166.437 121.833 167.198H121.732V166.106H120.418V173ZM133.348 169.477H135.354L137.22 173H138.864L136.808 169.255C137.925 168.88 138.585 167.877 138.585 166.64C138.585 164.932 137.398 163.84 135.551 163.84H131.926V173H133.348V169.477ZM133.348 165.034H135.354C136.452 165.034 137.125 165.649 137.125 166.671C137.125 167.719 136.49 168.309 135.392 168.309H133.348V165.034ZM144.78 171.153C144.526 171.711 143.961 172.016 143.136 172.016C142.044 172.016 141.34 171.229 141.295 169.979V169.915H146.177V169.445C146.177 167.287 145.015 165.973 143.092 165.973C141.143 165.973 139.905 167.376 139.905 169.566C139.905 171.769 141.118 173.133 143.098 173.133C144.679 173.133 145.796 172.372 146.094 171.153H144.78ZM143.085 167.084C144.095 167.084 144.755 167.814 144.787 168.931H141.295C141.372 167.82 142.076 167.084 143.085 167.084ZM153.667 168.322C153.489 166.982 152.442 165.973 150.709 165.973C148.697 165.973 147.472 167.338 147.472 169.528C147.472 171.762 148.703 173.133 150.715 173.133C152.423 173.133 153.489 172.175 153.667 170.816H152.34C152.163 171.565 151.572 171.972 150.709 171.972C149.573 171.972 148.868 171.051 148.868 169.528C148.868 168.036 149.566 167.135 150.709 167.135C151.623 167.135 152.182 167.649 152.34 168.322H153.667ZM159.761 171.153C159.507 171.711 158.942 172.016 158.117 172.016C157.025 172.016 156.32 171.229 156.276 169.979V169.915H161.157V169.445C161.157 167.287 159.996 165.973 158.072 165.973C156.124 165.973 154.886 167.376 154.886 169.566C154.886 171.769 156.098 173.133 158.079 173.133C159.659 173.133 160.776 172.372 161.075 171.153H159.761ZM158.066 167.084C159.075 167.084 159.735 167.814 159.767 168.931H156.276C156.352 167.82 157.057 167.084 158.066 167.084ZM162.795 173H164.16V168.957C164.16 167.846 164.801 167.16 165.81 167.16C166.819 167.16 167.302 167.719 167.302 168.861V173H168.667V168.538C168.667 166.894 167.816 165.973 166.273 165.973C165.232 165.973 164.547 166.437 164.21 167.198H164.109V166.106H162.795V173ZM171.072 164.367V166.125H169.974V167.217H171.072V171.185C171.072 172.511 171.675 173.044 173.192 173.044C173.459 173.044 173.713 173.013 173.935 172.975V171.889C173.745 171.908 173.624 171.921 173.415 171.921C172.735 171.921 172.437 171.597 172.437 170.854V167.217H173.935V166.125H172.437V164.367H171.072Z" fill="var(--vscode-foreground)"/>
<g filter="url(#filter5_i)">
<path d="M314 161C314 159.895 314.895 159 316 159H333C334.105 159 335 159.895 335 161V175C335 176.105 334.105 177 333 177H316C314.895 177 314 176.105 314 175V161Z" fill="var(--vscode-keybindingLabel-background)"/>
<path d="M320.516 167.172C320.404 167.289 320.346 167.43 320.346 167.594C320.346 167.928 320.604 168.186 320.938 168.186C321.102 168.186 321.254 168.115 321.365 168.004L324.5 164.84L327.629 168.004C327.746 168.121 327.898 168.186 328.057 168.186C328.391 168.186 328.648 167.928 328.648 167.594C328.648 167.424 328.59 167.289 328.479 167.178L324.951 163.615C324.828 163.48 324.67 163.416 324.5 163.416C324.33 163.416 324.178 163.48 324.043 163.615L320.516 167.172Z" fill="var(--vscode-foreground)"/>
</g>
<g filter="url(#filter6_i)">
<path d="M339 161C339 159.895 339.895 159 341 159H356C357.105 159 358 159.895 358 161V175C358 176.105 357.105 177 356 177H341C339.895 177 339 176.105 339 175V161Z" fill="var(--vscode-keybindingLabel-background)"/>
<path d="M346.865 168.748H348.717L350.439 172H351.957L350.059 168.543C351.09 168.197 351.699 167.271 351.699 166.129C351.699 164.553 350.604 163.545 348.898 163.545H345.553V172H346.865V168.748ZM346.865 164.646H348.717C349.73 164.646 350.352 165.215 350.352 166.158C350.352 167.125 349.766 167.67 348.752 167.67H346.865V164.646Z" fill="var(--vscode-foreground)"/>

</g>
<rect x="64" y="189" width="88" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity="0.25"/>
<rect x="158" y="189" width="63" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity="0.25"/>
<g filter="url(#filter7_i)">
<path d="M314 185C314 183.895 314.895 183 316 183H334C335.105 183 336 183.895 336 185V199C336 200.105 335.105 201 334 201H316C314.895 201 314 200.105 314 199V185Z" fill="var(--vscode-keybindingLabel-background)"/>

</g>
<g filter="url(#filter8_i)">
<path d="M340 185C340 183.895 340.895 183 342 183H357C358.105 183 359 183.895 359 185V199C359 200.105 358.105 201 357 201H342C340.895 201 340 200.105 340 199V185Z" fill="var(--vscode-keybindingLabel-background)"/>

</g>
<rect x="64" y="213" width="65" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity="0.25"/>
<rect x="135" y="213" width="86" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity="0.25"/>
<g filter="url(#filter9_i)">
<path d="M314 209C314 207.895 314.895 207 316 207H331C332.105 207 333 207.895 333 209V223C333 224.105 332.105 225 331 225H316C314.895 225 314 224.105 314 223V209Z" fill="var(--vscode-keybindingLabel-background)"/>

</g>
<g filter="url(#filter10_i)">
<path d="M337 209C337 207.895 337.895 207 339 207H357C358.105 207 359 207.895 359 209V223C359 224.105 358.105 225 357 225H339C337.895 225 337 224.105 337 223V209Z" fill="var(--vscode-keybindingLabel-background)"/>

</g>
<g filter="url(#filter11_i)">
<path d="M363 209C363 207.895 363.895 207 365 207H380C381.105 207 382 207.895 382 209V223C382 224.105 381.105 225 380 225H365C363.895 225 363 224.105 363 223V209Z" fill="var(--vscode-keybindingLabel-background)"/>

</g>
</g>
<defs>
<filter id="filter0_d" x="20" y="22" width="479" height="232" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="6"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter1_i" x="314" y="111" width="22" height="18" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.266667 0 0 0 0 0.266667 0 0 0 0 0.266667 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter2_i" x="340" y="111" width="20" height="18" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.266667 0 0 0 0 0.266667 0 0 0 0 0.266667 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter3_i" x="314" y="135" width="22" height="18" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.266667 0 0 0 0 0.266667 0 0 0 0 0.266667 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter4_i" x="340" y="135" width="20" height="18" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.266667 0 0 0 0 0.266667 0 0 0 0 0.266667 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter5_i" x="314" y="159" width="21" height="18" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.266667 0 0 0 0 0.266667 0 0 0 0 0.266667 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter6_i" x="339" y="159" width="19" height="18" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.266667 0 0 0 0 0.266667 0 0 0 0 0.266667 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter7_i" x="314" y="183" width="22" height="18" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.266667 0 0 0 0 0.266667 0 0 0 0 0.266667 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter8_i" x="340" y="183" width="19" height="18" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.266667 0 0 0 0 0.266667 0 0 0 0 0.266667 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter9_i" x="314" y="207" width="19" height="18" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.266667 0 0 0 0 0.266667 0 0 0 0 0.266667 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter10_i" x="337" y="207" width="22" height="18" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.266667 0 0 0 0 0.266667 0 0 0 0 0.266667 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter11_i" x="363" y="207" width="19" height="18" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.266667 0 0 0 0 0.266667 0 0 0 0 0.266667 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<clipPath id="clip0">
<rect width="115.654" height="39.2998" fill="white"/>
</clipPath>
</defs>
</svg>
