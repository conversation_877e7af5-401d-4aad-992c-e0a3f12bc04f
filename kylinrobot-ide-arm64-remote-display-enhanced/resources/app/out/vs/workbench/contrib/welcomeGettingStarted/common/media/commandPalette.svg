<svg viewBox="0 0 520 260" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="520" height="260" fill="var(--vscode-editor-background, #1e1e1e)"/>
<g clip-path="url(#clip0)">
<rect width="520" height="39" fill="var(--vscode-editorGroupHeader-tabsBackground, #252526)"/>
<g clip-path="url(#clip1)">
<rect width="115.654" height="39.2998" fill="var(--vscode-tab-activeBackground, #1e1e1e)"/>
<rect x="13.4742" y="15.7199" width="88.7052" height="7.85995" rx="3.92998" fill="var(--vscode-tab-unfocusedInactiveForeground, #454545)"/>
</g>
</g>
<g filter="url(#filter0_d)">
<rect width="425" height="182" transform="translate(48 20)" fill="var(--vscode-quickInput-background, #252526)"/>
<rect x="55.5" y="27.5" width="410" height="25" fill="var(--vscode-input-background, #3c3c3c)"/>
<path d="M61.168 43.3242V44.708L67.4648 41.9595V40.7598L61.168 38.0239V39.4204L65.8779 41.3691V41.4326L61.168 43.3242ZM73.6665 45H75.0312V39.2173H76.5547V38.1255H75.0122V37.4463C75.0122 36.748 75.3232 36.3989 76.085 36.3989C76.3008 36.3989 76.4849 36.4116 76.6118 36.4307V35.4023C76.377 35.3643 76.1104 35.3389 75.7993 35.3389C74.3647 35.3389 73.6665 36.0117 73.6665 37.3828V38.1255H72.5303V39.2173H73.6665V45ZM78.8081 36.875C79.2842 36.875 79.6714 36.4878 79.6714 36.0181C79.6714 35.542 79.2842 35.1548 78.8081 35.1548C78.332 35.1548 77.9448 35.542 77.9448 36.0181C77.9448 36.4878 78.332 36.875 78.8081 36.875ZM78.1289 45H79.4873V38.1064H78.1289V45ZM81.5566 45H82.9214V35.4023H81.5566V45ZM89.5039 43.1528C89.25 43.7114 88.6851 44.0161 87.8599 44.0161C86.7681 44.0161 86.0635 43.229 86.019 41.9785V41.915H90.9004V41.4453C90.9004 39.2871 89.7388 37.9731 87.8154 37.9731C85.8667 37.9731 84.6289 39.376 84.6289 41.5659C84.6289 43.7686 85.8413 45.1333 87.8218 45.1333C89.4023 45.1333 90.5195 44.3716 90.8179 43.1528H89.5039ZM87.8091 39.084C88.8184 39.084 89.4785 39.814 89.5103 40.9312H86.019C86.0952 39.8203 86.7998 39.084 87.8091 39.084Z" fill="var(--vscode-input-foreground, #cccccc)"/>
<rect x="55.5" y="27.5" width="410" height="25" stroke="var(--vscode-focusBorder, #0078d4)"/>
<rect x="60" y="66" width="38" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity="0.25"/>
<rect x="135" y="66" width="105" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity="0.25"/>
<rect x="102" y="66" width="29" height="6" rx="3" fill="var(--vscode-pickerGroup-foreground)"/>
<rect width="425" height="24" transform="translate(48 82)" fill="var(--vscode-quickInputList-focusBackground, #062F4A)"/>
<path d="M63.0278 99V95.3945H66.7539V93.8774H63.0278V91.4209H67.1094V89.8403H61.1108V99H63.0278ZM69.6357 90.9385C70.207 90.9385 70.6768 90.4814 70.6768 89.9102C70.6768 89.3325 70.207 88.8755 69.6357 88.8755C69.0645 88.8755 68.5884 89.3325 68.5884 89.9102C68.5884 90.4814 69.0645 90.9385 69.6357 90.9385ZM68.709 99H70.5562V92.0112H68.709V99ZM72.4922 99H74.3394V89.3389H72.4922V99ZM80.7886 97.0259C80.5918 97.502 80.0903 97.7686 79.3604 97.7686C78.3955 97.7686 77.7861 97.1021 77.7607 96.0356V95.9404H82.5659V95.3882C82.5659 93.1792 81.3472 91.8589 79.2842 91.8589C77.2021 91.8589 75.9199 93.2681 75.9199 95.5405C75.9199 97.8003 77.1768 99.146 79.3096 99.146C81.0234 99.146 82.2358 98.3271 82.5088 97.0259H80.7886ZM79.2905 93.2363C80.1538 93.2363 80.7124 93.833 80.7505 94.7915H77.7671C77.8306 93.8521 78.4336 93.2363 79.2905 93.2363Z" fill="var(--vscode-list-focusHighlightForeground)"/>
<path d="M85.416 94.6519C86.1016 94.6519 86.5396 94.1948 86.5396 93.5728C86.5396 92.9507 86.1016 92.5 85.416 92.5C84.7368 92.5 84.2925 92.9507 84.2925 93.5728C84.2925 94.1948 84.7368 94.6519 85.416 94.6519ZM85.416 99.1587C86.1016 99.1587 86.5396 98.7017 86.5396 98.0796C86.5396 97.4575 86.1016 97.0068 85.416 97.0068C84.7368 97.0068 84.2925 97.4575 84.2925 98.0796C84.2925 98.7017 84.7368 99.1587 85.416 99.1587ZM93.7632 99V92.3477H93.8647L98.5684 99H99.8506V89.8403H98.4668V96.5054H98.3652L93.6616 89.8403H92.3794V99H93.7632ZM106.528 97.1528C106.274 97.7114 105.709 98.0161 104.884 98.0161C103.792 98.0161 103.088 97.229 103.043 95.9785V95.915H107.925V95.4453C107.925 93.2871 106.763 91.9731 104.84 91.9731C102.891 91.9731 101.653 93.376 101.653 95.5659C101.653 97.7686 102.866 99.1333 104.846 99.1333C106.427 99.1333 107.544 98.3716 107.842 97.1528H106.528ZM104.833 93.084C105.843 93.084 106.503 93.814 106.535 94.9312H103.043C103.12 93.8203 103.824 93.084 104.833 93.084ZM118.513 92.1064H117.148L115.917 97.4258H115.809L114.387 92.1064H113.079L111.657 97.4258H111.556L110.318 92.1064H108.934L110.838 99H112.241L113.663 93.8584H113.771L115.199 99H116.615L118.513 92.1064ZM125.044 89.8403H123.623V95.8452C123.623 97.8257 125.038 99.2222 127.33 99.2222C129.634 99.2222 131.043 97.8257 131.043 95.8452V89.8403H129.621V95.731C129.621 97.0386 128.79 97.959 127.33 97.959C125.876 97.959 125.044 97.0386 125.044 95.731V89.8403ZM133.176 99H134.541V94.9565C134.541 93.8457 135.182 93.1602 136.191 93.1602C137.2 93.1602 137.683 93.7188 137.683 94.8613V99H139.047V94.5376C139.047 92.8936 138.197 91.9731 136.654 91.9731C135.613 91.9731 134.928 92.4365 134.591 93.1982H134.49V92.1064H133.176V99ZM141.453 90.3672V92.1255H140.355V93.2173H141.453V97.1846C141.453 98.5112 142.056 99.0444 143.573 99.0444C143.84 99.0444 144.094 99.0127 144.316 98.9746V97.8892C144.125 97.9082 144.005 97.9209 143.795 97.9209C143.116 97.9209 142.818 97.5972 142.818 96.8545V93.2173H144.316V92.1255H142.818V90.3672H141.453ZM146.626 90.875C147.103 90.875 147.49 90.4878 147.49 90.0181C147.49 89.542 147.103 89.1548 146.626 89.1548C146.15 89.1548 145.763 89.542 145.763 90.0181C145.763 90.4878 146.15 90.875 146.626 90.875ZM145.947 99H147.306V92.1064H145.947V99ZM149.794 90.3672V92.1255H148.696V93.2173H149.794V97.1846C149.794 98.5112 150.397 99.0444 151.914 99.0444C152.181 99.0444 152.435 99.0127 152.657 98.9746V97.8892C152.466 97.9082 152.346 97.9209 152.136 97.9209C151.457 97.9209 151.159 97.5972 151.159 96.8545V93.2173H152.657V92.1255H151.159V90.3672H149.794ZM154.326 99H155.691V89.4023H154.326V99ZM162.273 97.1528C162.02 97.7114 161.455 98.0161 160.629 98.0161C159.538 98.0161 158.833 97.229 158.789 95.9785V95.915H163.67V95.4453C163.67 93.2871 162.508 91.9731 160.585 91.9731C158.636 91.9731 157.398 93.376 157.398 95.5659C157.398 97.7686 158.611 99.1333 160.591 99.1333C162.172 99.1333 163.289 98.3716 163.587 97.1528H162.273ZM160.579 93.084C161.588 93.084 162.248 93.814 162.28 94.9312H158.789C158.865 93.8203 159.569 93.084 160.579 93.084ZM167.834 99.1143C168.786 99.1143 169.592 98.6636 170.005 97.9082H170.113V99H171.42V89.4023H170.056V93.1982H169.954C169.58 92.4429 168.78 91.9858 167.834 91.9858C166.088 91.9858 164.965 93.376 164.965 95.5469C164.965 97.7305 166.076 99.1143 167.834 99.1143ZM168.221 93.1538C169.364 93.1538 170.081 94.0806 170.081 95.5532C170.081 97.0386 169.37 97.9463 168.221 97.9463C167.066 97.9463 166.374 97.0513 166.374 95.5532C166.374 94.0615 167.072 93.1538 168.221 93.1538ZM178.606 99V95.166H182.542V93.9663H178.606V91.0654H182.903V89.8403H177.184V99H178.606ZM185.296 90.875C185.772 90.875 186.16 90.4878 186.16 90.0181C186.16 89.542 185.772 89.1548 185.296 89.1548C184.82 89.1548 184.433 89.542 184.433 90.0181C184.433 90.4878 184.82 90.875 185.296 90.875ZM184.617 99H185.976V92.1064H184.617V99ZM188.045 99H189.41V89.4023H188.045V99ZM195.992 97.1528C195.738 97.7114 195.173 98.0161 194.348 98.0161C193.256 98.0161 192.552 97.229 192.507 95.9785V95.915H197.389V95.4453C197.389 93.2871 196.227 91.9731 194.304 91.9731C192.355 91.9731 191.117 93.376 191.117 95.5659C191.117 97.7686 192.33 99.1333 194.31 99.1333C195.891 99.1333 197.008 98.3716 197.306 97.1528H195.992ZM194.297 93.084C195.307 93.084 195.967 93.814 195.999 94.9312H192.507C192.583 93.8203 193.288 93.084 194.297 93.084Z" fill="var(--vscode-quickInputList-focusForeground, #E3E3E3)"/>
<rect x="60" y="116" width="62.5" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity="0.25"/>
<rect x="126.5" y="116" width="29" height="6" rx="3" fill="var(--vscode-pickerGroup-foreground)"/>
<rect x="159.5" y="116" width="62.5" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity="0.25"/>
<rect x="60" y="138" width="41" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity="0.25"/>
<rect x="105" y="138" width="69" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity="0.25"/>
<rect x="178" y="138" width="26" height="6" rx="3" fill="var(--vscode-pickerGroup-foreground)"/>
<rect x="60" y="160" width="26" height="6" rx="3" fill="var(--vscode-pickerGroup-foreground)"/>
<rect x="90" y="160" width="164" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity="0.25"/>
<rect x="60" y="182" width="46" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity="0.25"/>
<rect x="110" y="182" width="29" height="6" rx="3" fill="var(--vscode-pickerGroup-foreground)"/>
<rect x="143" y="182" width="46" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity="0.25"/>
</g>
<defs>
<filter id="filter0_d" x="36" y="10" width="449" height="206" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="6"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<clipPath id="clip0">
<rect width="520" height="220" fill="white"/>
</clipPath>
<clipPath id="clip1">
<rect width="115.654" height="39.2998" fill="white"/>
</clipPath>
</defs>
</svg>
