<svg viewBox="0 0 520 216" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="520" height="214" fill="var(--vscode-editor-background)"/>
<g clip-path="url(#clip0)">
<rect width="520" height="39" fill="var(--vscode-editorGroupHeader-tabsBackground)"/>
<g clip-path="url(#clip1)">
<rect width="115.654" height="39.2998" fill="var(--vscode-tab-activeBackground)"/>
<rect x="13.4741" y="15.7197" width="88.7052" height="7.85995" rx="3.92998" fill="var(--vscode-foreground)" fill-opacity="0.25"/>
</g>
</g>
<g filter="url(#filter0_d)">
<rect width="425" height="182" transform="translate(48 20)" fill="var(--vscode-quickInput-background)"/>
<rect x="55.5" y="27.5" width="410" height="25" fill="var(--vscode-input-background)"/>
<path d="M60.7173 42.5562C60.8188 44.1812 62.1836 45.2222 64.2148 45.2222C66.3857 45.2222 67.7441 44.1304 67.7441 42.3975C67.7441 41.0264 66.9697 40.271 65.0908 39.8203L64.0815 39.5728C62.8882 39.2808 62.4058 38.8999 62.4058 38.2334C62.4058 37.3765 63.1548 36.8242 64.2783 36.8242C65.3447 36.8242 66.0811 37.3574 66.2144 38.2271H67.5981C67.5156 36.6973 66.1572 35.6182 64.2974 35.6182C62.2979 35.6182 60.9648 36.6973 60.9648 38.3096C60.9648 39.6426 61.7202 40.4297 63.377 40.8296L64.5576 41.1152C65.77 41.4072 66.3032 41.8516 66.3032 42.5688C66.3032 43.4067 65.4653 44.0098 64.3228 44.0098C63.0977 44.0098 62.2471 43.4448 62.1265 42.5562H60.7173ZM73.9902 43.1528C73.7363 43.7114 73.1714 44.0161 72.3462 44.0161C71.2544 44.0161 70.5498 43.229 70.5054 41.9785V41.915H75.3867V41.4453C75.3867 39.2871 74.2251 37.9731 72.3018 37.9731C70.353 37.9731 69.1152 39.376 69.1152 41.5659C69.1152 43.7686 70.3276 45.1333 72.3081 45.1333C73.8887 45.1333 75.0059 44.3716 75.3042 43.1528H73.9902ZM72.2954 39.084C73.3047 39.084 73.9648 39.814 73.9966 40.9312H70.5054C70.5815 39.8203 71.2861 39.084 72.2954 39.084ZM77.0879 45H78.4526V35.4023H77.0879V45ZM85.0352 43.1528C84.7812 43.7114 84.2163 44.0161 83.3911 44.0161C82.2993 44.0161 81.5947 43.229 81.5503 41.9785V41.915H86.4316V41.4453C86.4316 39.2871 85.27 37.9731 83.3467 37.9731C81.3979 37.9731 80.1602 39.376 80.1602 41.5659C80.1602 43.7686 81.3726 45.1333 83.353 45.1333C84.9336 45.1333 86.0508 44.3716 86.3491 43.1528H85.0352ZM83.3403 39.084C84.3496 39.084 85.0098 39.814 85.0415 40.9312H81.5503C81.6265 39.8203 82.3311 39.084 83.3403 39.084ZM93.9219 40.3218C93.7441 38.9824 92.6968 37.9731 90.9639 37.9731C88.9517 37.9731 87.7266 39.3379 87.7266 41.5278C87.7266 43.7622 88.958 45.1333 90.9702 45.1333C92.6777 45.1333 93.7441 44.1748 93.9219 42.8164H92.5952C92.4175 43.5654 91.8271 43.9717 90.9639 43.9717C89.8276 43.9717 89.123 43.0513 89.123 41.5278C89.123 40.0361 89.8213 39.1348 90.9639 39.1348C91.8779 39.1348 92.4365 39.6489 92.5952 40.3218H93.9219ZM96.0674 36.3672V38.1255H94.9692V39.2173H96.0674V43.1846C96.0674 44.5112 96.6704 45.0444 98.1875 45.0444C98.4541 45.0444 98.708 45.0127 98.9302 44.9746V43.8892C98.7397 43.9082 98.6191 43.9209 98.4097 43.9209C97.7305 43.9209 97.4321 43.5972 97.4321 42.8545V39.2173H98.9302V38.1255H97.4321V36.3672H96.0674ZM104.573 36.3672V38.1255H103.475V39.2173H104.573V43.1846C104.573 44.5112 105.176 45.0444 106.693 45.0444C106.96 45.0444 107.214 45.0127 107.436 44.9746V43.8892C107.246 43.9082 107.125 43.9209 106.916 43.9209C106.236 43.9209 105.938 43.5972 105.938 42.8545V39.2173H107.436V38.1255H105.938V36.3672H104.573ZM109.105 45H110.47V40.9629C110.47 39.8838 111.092 39.1665 112.209 39.1665C113.174 39.1665 113.688 39.7314 113.688 40.8677V45H115.053V40.5439C115.053 38.9126 114.146 37.9795 112.673 37.9795C111.632 37.9795 110.902 38.4365 110.565 39.2046H110.458V35.4023H109.105V45ZM121.509 43.1528C121.255 43.7114 120.69 44.0161 119.865 44.0161C118.773 44.0161 118.068 43.229 118.024 41.9785V41.915H122.905V41.4453C122.905 39.2871 121.744 37.9731 119.82 37.9731C117.872 37.9731 116.634 39.376 116.634 41.5659C116.634 43.7686 117.846 45.1333 119.827 45.1333C121.407 45.1333 122.524 44.3716 122.823 43.1528H121.509ZM119.814 39.084C120.823 39.084 121.483 39.814 121.515 40.9312H118.024C118.1 39.8203 118.805 39.084 119.814 39.084ZM128.58 36.3672V38.1255H127.482V39.2173H128.58V43.1846C128.58 44.5112 129.183 45.0444 130.7 45.0444C130.967 45.0444 131.221 45.0127 131.443 44.9746V43.8892C131.252 43.9082 131.132 43.9209 130.922 43.9209C130.243 43.9209 129.945 43.5972 129.945 42.8545V39.2173H131.443V38.1255H129.945V36.3672H128.58ZM134.959 45.1143C135.867 45.1143 136.623 44.7207 137.035 44.0225H137.143V45H138.457V40.2837C138.457 38.8364 137.479 37.9731 135.747 37.9731C134.179 37.9731 133.062 38.7285 132.922 39.8901H134.242C134.395 39.3887 134.921 39.103 135.683 39.103C136.616 39.103 137.099 39.5283 137.099 40.2837V40.8867L135.226 41.001C133.582 41.1025 132.655 41.8198 132.655 43.0576C132.655 44.3145 133.626 45.1143 134.959 45.1143ZM135.309 44.0161C134.566 44.0161 134.026 43.6416 134.026 43.0005C134.026 42.3721 134.458 42.0356 135.41 41.9722L137.099 41.8579V42.4546C137.099 43.3433 136.337 44.0161 135.309 44.0161ZM140.241 40.0361C140.241 41.0581 140.85 41.6421 142.164 41.9468L143.37 42.2324C144.062 42.3975 144.386 42.6768 144.386 43.1084C144.386 43.6733 143.783 44.0669 142.932 44.0669C142.107 44.0669 141.599 43.7305 141.428 43.1909H140.063C140.184 44.3906 141.256 45.1333 142.9 45.1333C144.551 45.1333 145.763 44.2637 145.763 42.9814C145.763 41.9785 145.147 41.4136 143.833 41.1089L142.685 40.8423C141.936 40.6646 141.586 40.3979 141.586 39.9727C141.586 39.4141 142.164 39.0396 142.926 39.0396C143.7 39.0396 144.195 39.376 144.322 39.8901H145.63C145.497 38.6968 144.481 37.9731 142.926 37.9731C141.364 37.9731 140.241 38.8364 140.241 40.0361ZM148.943 41.0137H148.835V35.4023H147.471V45H148.835V42.4927L149.438 41.915L151.812 45H153.488L150.454 41.0581L153.298 38.1064H151.686L148.943 41.0137ZM158.782 36.3672V38.1255H157.684V39.2173H158.782V43.1846C158.782 44.5112 159.385 45.0444 160.902 45.0444C161.169 45.0444 161.423 45.0127 161.645 44.9746V43.8892C161.455 43.9082 161.334 43.9209 161.125 43.9209C160.445 43.9209 160.147 43.5972 160.147 42.8545V39.2173H161.645V38.1255H160.147V36.3672H158.782ZM166.095 45.1333C168.113 45.1333 169.351 43.7812 169.351 41.5532C169.351 39.3252 168.107 37.9731 166.095 37.9731C164.076 37.9731 162.832 39.3315 162.832 41.5532C162.832 43.7812 164.07 45.1333 166.095 45.1333ZM166.095 43.9717C164.908 43.9717 164.241 43.0894 164.241 41.5532C164.241 40.0171 164.908 39.1284 166.095 39.1284C167.275 39.1284 167.948 40.0171 167.948 41.5532C167.948 43.083 167.275 43.9717 166.095 43.9717ZM174.55 45H175.915V40.8804C175.915 39.9092 176.645 39.2427 177.679 39.2427C177.92 39.2427 178.327 39.2871 178.441 39.3188V38.0366C178.295 38.0049 178.035 37.9858 177.832 37.9858C176.93 37.9858 176.162 38.4746 175.965 39.1538H175.864V38.1064H174.55V45ZM185.582 38.1064H184.217V42.1499C184.217 43.2607 183.614 43.9336 182.51 43.9336C181.507 43.9336 181.056 43.3877 181.056 42.2451V38.1064H179.691V42.5815C179.691 44.2002 180.542 45.1333 182.072 45.1333C183.119 45.1333 183.824 44.689 184.16 43.9082H184.268V45H185.582V38.1064ZM187.562 45H188.927V40.9565C188.927 39.8457 189.568 39.1602 190.578 39.1602C191.587 39.1602 192.069 39.7188 192.069 40.8613V45H193.434V40.5376C193.434 38.8936 192.583 37.9731 191.041 37.9731C190 37.9731 189.314 38.4365 188.978 39.1982H188.876V38.1064H187.562V45Z" fill="var(--vscode-input-foreground)"/>
<rect x="55.5" y="27.5" width="410" height="25" stroke="var(--vscode-focusBorder)"/>
<rect width="425" height="25" transform="translate(48 60)" fill="var(--vscode-quickInputList-focusBackground)"/>
<path d="M60.9902 77H62.355V72.9565C62.355 71.8457 62.9961 71.1602 64.0054 71.1602C65.0146 71.1602 65.4971 71.7188 65.4971 72.8613V77H66.8618V72.5376C66.8618 70.8936 66.0112 69.9731 64.4688 69.9731C63.4277 69.9731 62.7422 70.4365 62.4058 71.1982H62.3042V70.1064H60.9902V77ZM72.3716 69.9858C71.4321 69.9858 70.6196 70.4619 70.2007 71.249H70.0991V70.1064H68.7852V79.2979H70.1499V75.959H70.2578C70.6196 76.689 71.4004 77.1143 72.3843 77.1143C74.1299 77.1143 75.2407 75.7305 75.2407 73.5532C75.2407 71.3633 74.1299 69.9858 72.3716 69.9858ZM71.9844 75.9463C70.8418 75.9463 70.1245 75.0259 70.1245 73.5532C70.1245 72.0742 70.8418 71.1538 71.9907 71.1538C73.146 71.1538 73.8379 72.0552 73.8379 73.5532C73.8379 75.0513 73.146 75.9463 71.9844 75.9463ZM76.8848 77H78.2495V72.7661C78.2495 71.8647 78.8779 71.1602 79.7095 71.1602C80.522 71.1602 81.0425 71.6489 81.0425 72.436V77H82.3818V72.6392C82.3818 71.8076 82.9595 71.1602 83.8418 71.1602C84.7368 71.1602 85.1812 71.6235 85.1812 72.5757V77H86.5459V72.2456C86.5459 70.811 85.7334 69.9731 84.3369 69.9731C83.3784 69.9731 82.585 70.4619 82.2358 71.2046H82.1279C81.8232 70.4619 81.1694 69.9731 80.2236 69.9731C79.3032 69.9731 78.605 70.4302 78.3003 71.2046H78.1987V70.1064H76.8848V77ZM89.5229 72.3281C90.0625 72.3281 90.4561 71.9282 90.4561 71.4141C90.4561 70.8999 90.0625 70.5 89.5229 70.5C88.9897 70.5 88.5898 70.8999 88.5898 71.4141C88.5898 71.9282 88.9897 72.3281 89.5229 72.3281ZM89.5229 77.0952C90.0625 77.0952 90.4561 76.6953 90.4561 76.1812C90.4561 75.667 90.0625 75.2671 89.5229 75.2671C88.9897 75.2671 88.5898 75.667 88.5898 76.1812C88.5898 76.6953 88.9897 77.0952 89.5229 77.0952ZM96.8228 68.875C97.2988 68.875 97.686 68.4878 97.686 68.0181C97.686 67.542 97.2988 67.1548 96.8228 67.1548C96.3467 67.1548 95.9595 67.542 95.9595 68.0181C95.9595 68.4878 96.3467 68.875 96.8228 68.875ZM96.1436 77H97.502V70.1064H96.1436V77ZM99.5078 77H100.873V72.9565C100.873 71.8457 101.514 71.1602 102.523 71.1602C103.532 71.1602 104.015 71.7188 104.015 72.8613V77H105.379V72.5376C105.379 70.8936 104.529 69.9731 102.986 69.9731C101.945 69.9731 101.26 70.4365 100.923 71.1982H100.822V70.1064H99.5078V77ZM107.144 72.0361C107.144 73.0581 107.753 73.6421 109.067 73.9468L110.273 74.2324C110.965 74.3975 111.289 74.6768 111.289 75.1084C111.289 75.6733 110.686 76.0669 109.835 76.0669C109.01 76.0669 108.502 75.7305 108.331 75.1909H106.966C107.087 76.3906 108.16 77.1333 109.804 77.1333C111.454 77.1333 112.667 76.2637 112.667 74.9814C112.667 73.9785 112.051 73.4136 110.737 73.1089L109.588 72.8423C108.839 72.6646 108.49 72.3979 108.49 71.9727C108.49 71.4141 109.067 71.0396 109.829 71.0396C110.604 71.0396 111.099 71.376 111.226 71.8901H112.533C112.4 70.6968 111.384 69.9731 109.829 69.9731C108.268 69.9731 107.144 70.8364 107.144 72.0361ZM114.742 68.3672V70.1255H113.644V71.2173H114.742V75.1846C114.742 76.5112 115.345 77.0444 116.862 77.0444C117.129 77.0444 117.383 77.0127 117.605 76.9746V75.8892C117.415 75.9082 117.294 75.9209 117.084 75.9209C116.405 75.9209 116.107 75.5972 116.107 74.8545V71.2173H117.605V70.1255H116.107V68.3672H114.742ZM121.122 77.1143C122.029 77.1143 122.785 76.7207 123.197 76.0225H123.305V77H124.619V72.2837C124.619 70.8364 123.642 69.9731 121.909 69.9731C120.341 69.9731 119.224 70.7285 119.084 71.8901H120.404C120.557 71.3887 121.083 71.103 121.845 71.103C122.778 71.103 123.261 71.5283 123.261 72.2837V72.8867L121.388 73.001C119.744 73.1025 118.817 73.8198 118.817 75.0576C118.817 76.3145 119.789 77.1143 121.122 77.1143ZM121.471 76.0161C120.728 76.0161 120.188 75.6416 120.188 75.0005C120.188 74.3721 120.62 74.0356 121.572 73.9722L123.261 73.8579V74.4546C123.261 75.3433 122.499 76.0161 121.471 76.0161ZM126.625 77H127.99V67.4023H126.625V77ZM130.104 77H131.468V67.4023H130.104V77Z" fill="var(--vscode-quickInputList-focusForeground)"/>
<path d="M60.9902 102H62.355V97.9565C62.355 96.8457 62.9961 96.1602 64.0054 96.1602C65.0146 96.1602 65.4971 96.7188 65.4971 97.8613V102H66.8618V97.5376C66.8618 95.8936 66.0112 94.9731 64.4688 94.9731C63.4277 94.9731 62.7422 95.4365 62.4058 96.1982H62.3042V95.1064H60.9902V102ZM72.3716 94.9858C71.4321 94.9858 70.6196 95.4619 70.2007 96.249H70.0991V95.1064H68.7852V104.298H70.1499V100.959H70.2578C70.6196 101.689 71.4004 102.114 72.3843 102.114C74.1299 102.114 75.2407 100.73 75.2407 98.5532C75.2407 96.3633 74.1299 94.9858 72.3716 94.9858ZM71.9844 100.946C70.8418 100.946 70.1245 100.026 70.1245 98.5532C70.1245 97.0742 70.8418 96.1538 71.9907 96.1538C73.146 96.1538 73.8379 97.0552 73.8379 98.5532C73.8379 100.051 73.146 100.946 71.9844 100.946ZM76.8848 102H78.2495V97.7661C78.2495 96.8647 78.8779 96.1602 79.7095 96.1602C80.522 96.1602 81.0425 96.6489 81.0425 97.436V102H82.3818V97.6392C82.3818 96.8076 82.9595 96.1602 83.8418 96.1602C84.7368 96.1602 85.1812 96.6235 85.1812 97.5757V102H86.5459V97.2456C86.5459 95.811 85.7334 94.9731 84.3369 94.9731C83.3784 94.9731 82.585 95.4619 82.2358 96.2046H82.1279C81.8232 95.4619 81.1694 94.9731 80.2236 94.9731C79.3032 94.9731 78.605 95.4302 78.3003 96.2046H78.1987V95.1064H76.8848V102ZM89.5229 97.3281C90.0625 97.3281 90.4561 96.9282 90.4561 96.4141C90.4561 95.8999 90.0625 95.5 89.5229 95.5C88.9897 95.5 88.5898 95.8999 88.5898 96.4141C88.5898 96.9282 88.9897 97.3281 89.5229 97.3281ZM89.5229 102.095C90.0625 102.095 90.4561 101.695 90.4561 101.181C90.4561 100.667 90.0625 100.267 89.5229 100.267C88.9897 100.267 88.5898 100.667 88.5898 101.181C88.5898 101.695 88.9897 102.095 89.5229 102.095ZM95.9595 97.0361C95.9595 98.0581 96.5688 98.6421 97.8828 98.9468L99.0889 99.2324C99.7808 99.3975 100.104 99.6768 100.104 100.108C100.104 100.673 99.5015 101.067 98.6509 101.067C97.8257 101.067 97.3179 100.73 97.1465 100.191H95.7817C95.9023 101.391 96.9751 102.133 98.6191 102.133C100.27 102.133 101.482 101.264 101.482 99.9814C101.482 98.9785 100.866 98.4136 99.5522 98.1089L98.4033 97.8423C97.6543 97.6646 97.3052 97.3979 97.3052 96.9727C97.3052 96.4141 97.8828 96.0396 98.6445 96.0396C99.4189 96.0396 99.9141 96.376 100.041 96.8901H101.349C101.215 95.6968 100.2 94.9731 98.6445 94.9731C97.083 94.9731 95.9595 95.8364 95.9595 97.0361ZM103.558 93.3672V95.1255H102.459V96.2173H103.558V100.185C103.558 101.511 104.161 102.044 105.678 102.044C105.944 102.044 106.198 102.013 106.42 101.975V100.889C106.23 100.908 106.109 100.921 105.9 100.921C105.221 100.921 104.922 100.597 104.922 99.8545V96.2173H106.42V95.1255H104.922V93.3672H103.558ZM109.937 102.114C110.845 102.114 111.6 101.721 112.013 101.022H112.121V102H113.435V97.2837C113.435 95.8364 112.457 94.9731 110.724 94.9731C109.156 94.9731 108.039 95.7285 107.899 96.8901H109.22C109.372 96.3887 109.899 96.103 110.661 96.103C111.594 96.103 112.076 96.5283 112.076 97.2837V97.8867L110.204 98.001C108.56 98.1025 107.633 98.8198 107.633 100.058C107.633 101.314 108.604 102.114 109.937 102.114ZM110.286 101.016C109.543 101.016 109.004 100.642 109.004 100C109.004 99.3721 109.436 99.0356 110.388 98.9722L112.076 98.8579V99.4546C112.076 100.343 111.314 101.016 110.286 101.016ZM115.377 102H116.742V97.8804C116.742 96.9092 117.472 96.2427 118.506 96.2427C118.748 96.2427 119.154 96.2871 119.268 96.3188V95.0366C119.122 95.0049 118.862 94.9858 118.659 94.9858C117.757 94.9858 116.989 95.4746 116.792 96.1538H116.691V95.1064H115.377V102ZM121.42 93.3672V95.1255H120.322V96.2173H121.42V100.185C121.42 101.511 122.023 102.044 123.54 102.044C123.807 102.044 124.061 102.013 124.283 101.975V100.889C124.092 100.908 123.972 100.921 123.762 100.921C123.083 100.921 122.785 100.597 122.785 99.8545V96.2173H124.283V95.1255H122.785V93.3672H121.42Z" fill="var(--vscode-foreground)"/>
<path d="M76.496 118.992H69.712L68.864 118.144L68.512 118H63.504L63.008 118.496V129.504L63.504 130H76.512L77.008 129.504V119.504L76.496 118.992ZM75.984 127.488V128.992H63.984V122.992H68.48L68.832 122.848L69.696 121.984H76L75.984 127.488ZM75.984 120.992H69.488L69.136 121.152L68.288 122.016H64V119.008H68.288L69.136 119.856L69.504 120.016H76L75.984 120.992Z" fill="var(--vscode-foreground)" />
<rect x="86" y="121" width="97" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity="0.25"/>
<path d="M76.496 146.992H69.712L68.864 146.144L68.512 146H63.504L63.008 146.496V157.504L63.504 158H76.512L77.008 157.504V147.504L76.496 146.992ZM75.984 155.488V156.992H63.984V150.992H68.48L68.832 150.848L69.696 149.984H76L75.984 155.488ZM75.984 148.992H69.488L69.136 149.152L68.288 150.016H64V147.008H68.288L69.136 147.856L69.504 148.016H76L75.984 148.992Z" fill="var(--vscode-foreground)" />
<rect x="86" y="149" width="63" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity="0.25"/>
<path d="M76.496 174.992H69.712L68.864 174.144L68.512 174H63.504L63.008 174.496V185.504L63.504 186H76.512L77.008 185.504V175.504L76.496 174.992ZM75.984 183.488V184.992H63.984V178.992H68.48L68.832 178.848L69.696 177.984H76L75.984 183.488ZM75.984 176.992H69.488L69.136 177.152L68.288 178.016H64V175.008H68.288L69.136 175.856L69.504 176.016H76L75.984 176.992Z" fill="var(--vscode-foreground)" />
<rect x="86" y="177" width="81" height="6" rx="3" fill="var(--vscode-foreground)" fill-opacity="0.25"/>
</g>
<defs>
<filter id="filter0_d" x="36" y="10" width="449" height="206" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="6"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<clipPath id="clip0">
<rect width="520" height="211" fill="white"/>
</clipPath>
<clipPath id="clip1">
<rect width="115.654" height="39.2998" fill="white"/>
</clipPath>
</defs>
</svg>
