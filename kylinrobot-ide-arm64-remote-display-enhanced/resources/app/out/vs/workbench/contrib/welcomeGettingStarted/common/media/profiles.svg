<svg fill="none" viewBox="0 0 520 238" xmlns="http://www.w3.org/2000/svg">
<rect fill="var(--vscode-breadcrumb-background, #1E1E1E)"/>
<g clip-path="url(#c)">
<rect width="520" height="39" fill="var(--vscode-breadcrumbPicker-background, #252526)"/>
<g clip-path="url(#b)">
<rect width="115.65" height="39.3" fill="var(--vscode-breadcrumb-background, #1E1E1E)"/>
<rect x="13.474" y="15.72" width="88.705" height="7.86" rx="3.93" fill="var(--vscode-tab-inactiveBackground, #2D2D2D)"/>
</g>
<path d="m215.24 20.222c-2.545 0-4.132-1.8409-4.132-4.7989 0-2.9643 1.58-4.8051 4.126-4.8051 2.018 0 3.58 1.2885 3.808 3.123h-1.415c-0.267-1.1362-1.187-1.8599-2.393-1.8599-1.644 0-2.673 1.3648-2.673 3.542 0 2.1773 1.029 3.5357 2.679 3.5357 1.213 0 2.133-0.6538 2.387-1.6948h1.415c-0.254 1.7837-1.758 2.958-3.802 2.958zm5.516-0.2222v-6.8936h1.314v1.0474h0.102c0.197-0.6792 0.965-1.168 1.866-1.168 0.203 0 0.463 0.0191 0.609 0.0508v1.2822c-0.114-0.0317-0.52-0.0761-0.761-0.0761-1.035 0-1.765 0.6665-1.765 1.6377v4.1196h-1.365zm9.56-1.8472h1.314c-0.299 1.2188-1.416 1.9805-2.996 1.9805-1.981 0-3.193-1.3647-3.193-3.5674 0-2.1899 1.238-3.5928 3.186-3.5928 1.924 0 3.085 1.314 3.085 3.4722v0.4697h-4.881v0.0635c0.044 1.2505 0.749 2.0376 1.841 2.0376 0.825 0 1.39-0.3047 1.644-0.8633zm-1.695-4.0688c-1.009 0-1.714 0.7363-1.79 1.8472h3.491c-0.032-1.1172-0.692-1.8472-1.701-1.8472zm6.64 6.0303c-1.333 0-2.305-0.7998-2.305-2.0567 0-1.2378 0.927-1.9551 2.571-2.0566l1.873-0.1143v-0.603c0-0.7554-0.483-1.1807-1.416-1.1807-0.761 0-1.288 0.2857-1.441 0.7871h-1.32c0.14-1.1616 1.257-1.917 2.825-1.917 1.733 0 2.71 0.8633 2.71 2.3106v4.7163h-1.314v-0.9775h-0.108c-0.412 0.6982-1.168 1.0918-2.075 1.0918zm0.349-1.0982c1.028 0 1.79-0.6728 1.79-1.5615v-0.5967l-1.689 0.1143c-0.952 0.0634-1.383 0.3999-1.383 1.0283 0 0.6411 0.539 1.0156 1.282 1.0156zm5.573-7.6489h1.365v1.7583h1.498v1.0918h-1.498v3.6372c0 0.7427 0.298 1.0664 0.977 1.0664 0.21 0 0.33-0.0127 0.521-0.0317v1.0854c-0.222 0.0381-0.476 0.0698-0.743 0.0698-1.517 0-2.12-0.5332-2.12-1.8598v-3.9673h-1.098v-1.0918h1.098v-1.7583zm8.925 6.7856h1.314c-0.299 1.2188-1.416 1.9805-2.996 1.9805-1.981 0-3.193-1.3647-3.193-3.5674 0-2.1899 1.238-3.5928 3.186-3.5928 1.924 0 3.085 1.314 3.085 3.4722v0.4697h-4.881v0.0635c0.044 1.2505 0.749 2.0376 1.841 2.0376 0.825 0 1.39-0.3047 1.644-0.8633zm-1.695-4.0688c-1.009 0-1.714 0.7363-1.79 1.8472h3.491c-0.032-1.1172-0.692-1.8472-1.701-1.8472zm8.442-3.2437h3.587c1.79 0 3.021 1.2315 3.021 3.0342 0 1.7647-1.269 2.9961-3.066 2.9961h-2.12v3.1294h-1.422v-9.1597zm1.422 1.1997v3.6372h1.79c1.225 0 1.943-0.6538 1.943-1.8027 0-1.187-0.692-1.8345-1.943-1.8345h-1.79zm6.862 7.96v-6.8936h1.314v1.0474h0.102c0.196-0.6792 0.964-1.168 1.866-1.168 0.203 0 0.463 0.0191 0.609 0.0508v1.2822c-0.114-0.0317-0.52-0.0761-0.762-0.0761-1.034 0-1.764 0.6665-1.764 1.6377v4.1196h-1.365zm7.947 0.1333c-2.025 0-3.262-1.3521-3.262-3.5801 0-2.2217 1.244-3.5801 3.262-3.5801 2.013 0 3.257 1.3521 3.257 3.5801s-1.238 3.5801-3.257 3.5801zm0-1.1616c1.181 0 1.854-0.8887 1.854-2.4185 0-1.5361-0.673-2.4248-1.854-2.4248-1.187 0-1.853 0.8887-1.853 2.4248 0 1.5362 0.666 2.4185 1.853 2.4185zm5.389 1.0283v-5.7827h-1.136v-1.0918h1.136v-0.7427c0-1.3711 0.699-2.0439 2.133-2.0439 0.311 0 0.578 0.0254 0.813 0.0634v1.0284c-0.127-0.0191-0.311-0.0318-0.527-0.0318-0.762 0-1.073 0.3491-1.073 1.0474v0.6792h1.543v1.0918h-1.524v5.7827h-1.365zm5.142-8.125c-0.476 0-0.863-0.3872-0.863-0.8569 0-0.4761 0.387-0.8633 0.863-0.8633s0.863 0.3872 0.863 0.8633c0 0.4697-0.387 0.8569-0.863 0.8569zm-0.679 8.125v-6.8936h1.358v6.8936h-1.358zm3.428 0v-9.5977h1.364v9.5977h-1.364zm7.947-1.8472h1.314c-0.298 1.2188-1.416 1.9805-2.996 1.9805-1.981 0-3.193-1.3647-3.193-3.5674 0-2.1899 1.238-3.5928 3.186-3.5928 1.924 0 3.085 1.314 3.085 3.4722v0.4697h-4.881v0.0635c0.044 1.2505 0.749 2.0376 1.841 2.0376 0.825 0 1.39-0.3047 1.644-0.8633zm-1.695-4.0688c-1.009 0-1.714 0.7363-1.79 1.8472h3.491c-0.031-1.1172-0.692-1.8472-1.701-1.8472zm5.789 6.0112c-0.533 0-0.933-0.3999-0.933-0.914 0-0.5142 0.4-0.9141 0.933-0.9141 0.54 0 0.933 0.3999 0.933 0.9141 0 0.5141-0.393 0.914-0.933 0.914zm4.101 0c-0.533 0-0.933-0.3999-0.933-0.914 0-0.5142 0.4-0.9141 0.933-0.9141 0.539 0 0.933 0.3999 0.933 0.9141 0 0.5141-0.394 0.914-0.933 0.914zm4.1 0c-0.533 0-0.933-0.3999-0.933-0.914 0-0.5142 0.4-0.9141 0.933-0.9141 0.54 0 0.933 0.3999 0.933 0.9141 0 0.5141-0.393 0.914-0.933 0.914z" fill="var(--vscode-quickInput-foreground, #FFFFFF)"/>
</g>
<g filter="url(#a)">
<rect transform="translate(47 24)" width="425" height="196" fill="var(--vscode-breadcrumbPicker-background, #252526)"/>
<rect x="54.5" y="31.5" width="410" height="25" fill="var(--vscode-checkbox-background, #3C3C3C)"/>
<rect x="54.5" y="31.5" width="410" height="25" stroke="var(--vscode-focusBorder, #007FD4)"/>
<rect transform="translate(47 64)" width="425" height="24" fill="var(--vscode-peekViewEditor-background, #062F4A)"/>
<path d="m65.963 79.775v1.2251h-5.8145v-9.1597h5.8145v1.2251h-4.3926v2.6787h4.1577v1.1807h-4.1577v2.8501h4.3926zm1.8979 1.2251v-6.8936h1.314v1.0982h0.1016c0.3046-0.7744 1.0029-1.2315 1.9233-1.2315 0.9458 0 1.5996 0.4888 1.9043 1.2315h0.1079c0.3491-0.7427 1.1426-1.2315 2.1011-1.2315 1.3965 0 2.209 0.8379 2.209 2.2725v4.7544h-1.3648v-4.4243c0-0.9522-0.4443-1.4155-1.3393-1.4155-0.8824 0-1.46 0.6474-1.46 1.479v4.3608h-1.3394v-4.564c0-0.7871-0.5205-1.2758-1.333-1.2758-0.8315 0-1.4599 0.7045-1.4599 1.6059v4.2339h-1.3648zm15.165-7.0142c1.7583 0 2.8691 1.3775 2.8691 3.5674 0 2.1773-1.1108 3.5611-2.8564 3.5611-0.9839 0-1.7647-0.4253-2.1265-1.1553h-0.1079v3.3389h-1.3647v-9.1915h1.3139v1.1426h0.1016c0.4189-0.7871 1.2314-1.2632 2.1709-1.2632zm-0.3872 5.9605c1.1616 0 1.8535-0.895 1.8535-2.3931 0-1.498-0.6919-2.3994-1.8472-2.3994-1.1489 0-1.8662 0.9204-1.8662 2.3994 0 1.4727 0.7173 2.3931 1.8599 2.3931zm5.3828-7.5791h1.3647v1.7583h1.4981v1.0918h-1.4981v3.6372c0 0.7427 0.2984 1.0664 0.9776 1.0664 0.2094 0 0.33-0.0127 0.5205-0.0317v1.0854c-0.2222 0.0381-0.4761 0.0698-0.7427 0.0698-1.5171 0-2.1201-0.5332-2.1201-1.8598v-3.9673h-1.0982v-1.0918h1.0982v-1.7583zm5.1543 11.134c-0.1143 0-0.438-0.0127-0.565-0.0318v-1.1171c0.108 0.019 0.3174 0.0253 0.4444 0.0253 0.6474 0 1.0156-0.2729 1.2505-1.0029l0.1015-0.3555-2.501-6.9126h1.4854l1.7329 5.5225h0.1079l1.7266-5.5225h1.4472l-2.5771 7.211c-0.5903 1.6567-1.2441 2.1836-2.6533 2.1836zm10.34-11.661h3.587c1.79 0 3.021 1.2315 3.021 3.0342 0 1.7647-1.27 2.9961-3.066 2.9961h-2.12v3.1294h-1.422v-9.1597zm1.422 1.1997v3.6372h1.79c1.225 0 1.942-0.6538 1.942-1.8027 0-1.187-0.691-1.8345-1.942-1.8345h-1.79zm6.862 7.96v-6.8936h1.314v1.0474h0.101c0.197-0.6792 0.965-1.168 1.867-1.168 0.203 0 0.463 0.0191 0.609 0.0508v1.2822c-0.114-0.0317-0.521-0.0761-0.762-0.0761-1.034 0-1.764 0.6665-1.764 1.6377v4.1196h-1.365zm7.947 0.1333c-2.025 0-3.263-1.3521-3.263-3.5801 0-2.2217 1.245-3.5801 3.263-3.5801 2.012 0 3.256 1.3521 3.256 3.5801s-1.237 3.5801-3.256 3.5801zm0-1.1616c1.181 0 1.854-0.8887 1.854-2.4185 0-1.5361-0.673-2.4248-1.854-2.4248-1.187 0-1.853 0.8887-1.853 2.4248 0 1.5362 0.666 2.4185 1.853 2.4185zm5.389 1.0283v-5.7827h-1.136v-1.0918h1.136v-0.7427c0-1.3711 0.698-2.0439 2.133-2.0439 0.311 0 0.578 0.0254 0.813 0.0634v1.0284c-0.127-0.0191-0.311-0.0318-0.527-0.0318-0.762 0-1.073 0.3491-1.073 1.0474v0.6792h1.542v1.0918h-1.523v5.7827h-1.365zm5.142-8.125c-0.476 0-0.863-0.3872-0.863-0.8569 0-0.4761 0.387-0.8633 0.863-0.8633s0.863 0.3872 0.863 0.8633c0 0.4697-0.387 0.8569-0.863 0.8569zm-0.679 8.125v-6.8936h1.358v6.8936h-1.358zm3.427 0v-9.5977h1.365v9.5977h-1.365zm7.948-1.8472h1.314c-0.299 1.2188-1.416 1.9805-2.996 1.9805-1.981 0-3.193-1.3647-3.193-3.5674 0-2.1899 1.237-3.5928 3.186-3.5928 1.923 0 3.085 1.314 3.085 3.4722v0.4697h-4.881v0.0635c0.044 1.2505 0.749 2.0376 1.841 2.0376 0.825 0 1.39-0.3047 1.644-0.8633zm-1.695-4.0688c-1.009 0-1.714 0.7363-1.79 1.8472h3.491c-0.032-1.1172-0.692-1.8472-1.701-1.8472z" fill="var(--vscode-quickInput-foreground, #FFFFFF)"/>
<rect x="59" y="98" width="62.5" height="6" rx="3" fill="var(--vscode-editorOverviewRuler-commonContentForeground, #606060)"/>
<rect x="125.5" y="98" width="29" height="6" rx="3" fill="var(--vscode-banner-iconForeground, #3794FF)" fill-opacity=".6"/>
<rect x="158.5" y="98" width="62.5" height="6" rx="3" fill="var(--vscode-editorOverviewRuler-commonContentForeground, #606060)"/>
<g fill="var(--vscode-quickInput-foreground, #FFFFFF)">
<path d="m60.149 123.84h3.5865c1.79 0 3.0214 1.232 3.0214 3.035 0 1.764-1.2695 2.996-3.0659 2.996h-2.1201v3.129h-1.4219v-9.16zm1.4219 1.2v3.637h1.79c1.2251 0 1.9424-0.654 1.9424-1.802 0-1.187-0.6919-1.835-1.9424-1.835h-1.79zm7.7632 10.461c-0.1143 0-0.438-0.013-0.565-0.032v-1.117c0.108 0.019 0.3174 0.025 0.4444 0.025 0.6474 0 1.0156-0.273 1.2505-1.002l0.1015-0.356-2.5009-6.913h1.4853l1.7329 5.523h0.1079l1.7266-5.523h1.4473l-2.5772 7.211c-0.5903 1.657-1.2441 2.184-2.6533 2.184zm7.1094-11.134h1.3647v1.758h1.4981v1.092h-1.4981v3.637c0 0.743 0.2983 1.067 0.9775 1.067 0.2095 0 0.3301-0.013 0.5206-0.032v1.086c-0.2222 0.038-0.4761 0.069-0.7427 0.069-1.5171 0-2.1201-0.533-2.1201-1.859v-3.968h-1.0982v-1.092h1.0982v-1.758zm4.5322 8.633v-9.598h1.352v3.803h0.1079c0.3365-0.768 1.0665-1.226 2.1075-1.226 1.4726 0 2.3803 0.934 2.3803 2.565v4.456h-1.3647v-4.132c0-1.137-0.5142-1.701-1.479-1.701-1.1172 0-1.7393 0.717-1.7393 1.796v4.037h-1.3647zm10.791 0.133c-2.0249 0-3.2627-1.352-3.2627-3.58 0-2.221 1.2441-3.58 3.2627-3.58 2.0122 0 3.2563 1.352 3.2563 3.58s-1.2377 3.58-3.2563 3.58zm0-1.161c1.1807 0 1.8535-0.889 1.8535-2.419 0-1.536-0.6728-2.425-1.8535-2.425-1.187 0-1.8535 0.889-1.8535 2.425s0.6665 2.419 1.8535 2.419zm4.9004 1.028v-6.894h1.314v1.092h0.1015c0.3364-0.761 1.022-1.225 2.0635-1.225 1.542 0 2.393 0.921 2.393 2.565v4.462h-1.365v-4.139c0-1.142-0.483-1.701-1.4919-1.701-1.0092 0-1.6504 0.686-1.6504 1.797v4.043h-1.3647z"/>
<path d="m60.149 149.84h3.3072c2.7168 0 4.31 1.676 4.31 4.558 0 2.914-1.5806 4.602-4.31 4.602h-3.3072v-9.16zm1.4219 1.225v6.71h1.7266c1.917 0 3.0214-1.225 3.0214-3.358 0-2.114-1.1235-3.352-3.0214-3.352h-1.7266zm10.886 8.068c-2.0249 0-3.2627-1.352-3.2627-3.58 0-2.221 1.2442-3.58 3.2627-3.58 2.0122 0 3.2564 1.352 3.2564 3.58s-1.2378 3.58-3.2564 3.58zm0-1.161c1.1807 0 1.8535-0.889 1.8535-2.419 0-1.536-0.6728-2.425-1.8535-2.425-1.187 0-1.8535 0.889-1.8535 2.425s0.6665 2.419 1.8535 2.419zm10.753-3.65h-1.3267c-0.1587-0.673-0.7173-1.187-1.6313-1.187-1.1426 0-1.8409 0.901-1.8409 2.393 0 1.523 0.7046 2.444 1.8409 2.444 0.8632 0 1.4536-0.407 1.6313-1.156h1.3267c-0.1778 1.359-1.2442 2.317-2.9517 2.317-2.0122 0-3.2437-1.371-3.2437-3.605 0-2.19 1.2251-3.555 3.2374-3.555 1.7329 0 2.7802 1.009 2.958 2.349zm10.467-2.171-1.9614 6.849h-1.3457l-2.4692-9.16h1.479l1.6821 7.122h0.0889l1.9106-7.122h1.3203l1.9234 7.122h0.0888l1.6758-7.122h1.479l-2.4756 9.16h-1.3393l-1.9678-6.849h-0.0889zm7.0268 6.849v-6.894h1.314v1.048h0.102c0.196-0.679 0.964-1.168 1.866-1.168 0.203 0 0.463 0.019 0.609 0.051v1.282c-0.114-0.032-0.52-0.076-0.762-0.076-1.034 0-1.764 0.666-1.764 1.637v4.12h-1.365zm5.91-8.125c-0.476 0-0.864-0.387-0.864-0.857 0-0.476 0.388-0.863 0.864-0.863s0.863 0.387 0.863 0.863c0 0.47-0.387 0.857-0.863 0.857zm-0.679 8.125v-6.894h1.358v6.894h-1.358zm3.846-8.633h1.365v1.758h1.498v1.092h-1.498v3.637c0 0.743 0.298 1.067 0.978 1.067 0.209 0 0.33-0.013 0.52-0.032v1.086c-0.222 0.038-0.476 0.069-0.743 0.069-1.517 0-2.12-0.533-2.12-1.859v-3.968h-1.098v-1.092h1.098v-1.758zm8.925 6.786h1.314c-0.298 1.219-1.416 1.98-2.996 1.98-1.981 0-3.193-1.364-3.193-3.567 0-2.19 1.238-3.593 3.187-3.593 1.923 0 3.085 1.314 3.085 3.472v0.47h-4.882v0.064c0.045 1.25 0.749 2.037 1.841 2.037 0.825 0 1.39-0.305 1.644-0.863zm-1.695-4.069c-1.009 0-1.714 0.736-1.79 1.847h3.491c-0.031-1.117-0.691-1.847-1.701-1.847zm4.729 5.916v-6.894h1.314v1.048h0.102c0.197-0.679 0.965-1.168 1.866-1.168 0.203 0 0.463 0.019 0.609 0.051v1.282c-0.114-0.032-0.52-0.076-0.761-0.076-1.035 0-1.765 0.666-1.765 1.637v4.12h-1.365z"/>
</g>
<rect x="59" y="178" width="26" height="6" rx="3" fill="var(--vscode-banner-iconForeground, #3794FF)" fill-opacity=".6"/>
<rect x="89" y="178" width="164" height="6" rx="3" fill="var(--vscode-editorOverviewRuler-commonContentForeground, #606060)"/>
<rect x="59" y="200" width="46" height="6" rx="3" fill="var(--vscode-editorOverviewRuler-commonContentForeground, #606060)"/>
<rect x="109" y="200" width="29" height="6" rx="3" fill="var(--vscode-banner-iconForeground, #3794FF)" fill-opacity=".6"/>
<rect x="142" y="200" width="46" height="6" rx="3" fill="var(--vscode-editorOverviewRuler-commonContentForeground, #606060)"/>
</g>
<defs>
<filter id="a" x="31" y="10" width="457" height="228" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="8"/>
<feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.36 0"/>
<feBlend in2="BackgroundImageFix" result="effect1_dropShadow_1_138"/>
<feBlend in="SourceGraphic" in2="effect1_dropShadow_1_138" result="shape"/>
</filter>
<clipPath id="c">
<rect width="520" height="238" fill="var(--vscode-activityBar-activeBorder, #FFFFFF)"/>
</clipPath>
<clipPath id="b">
<rect width="115.65" height="39.3" fill="var(--vscode-activityBar-activeBorder, #FFFFFF)"/>
</clipPath>
</defs>
</svg>
