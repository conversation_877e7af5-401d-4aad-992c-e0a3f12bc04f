{"version": 3, "sources": ["media/processExplorer.css", "../../../base/browser/ui/codicons/codicon/codicon.css", "../../../base/browser/ui/codicons/codicon/codicon-modifiers.css", "../../../base/browser/ui/aria/aria.css", "../../../base/browser/ui/list/list.css", "../../../base/browser/ui/scrollbar/media/scrollbars.css", "../../../base/browser/ui/selectBox/selectBoxCustom.css", "../../../base/browser/ui/selectBox/selectBox.css", "../../../base/browser/ui/actionbar/actionbar.css", "../../../base/browser/ui/toggle/toggle.css", "../../../base/browser/ui/inputbox/inputBox.css", "../../../base/browser/ui/findinput/findInput.css", "../../../base/browser/ui/tree/media/tree.css"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nhtml {\n\tfont-size: 13px;\n}\n\n/* Font Families (with CJK support) */\n\n.mac { font-family: -apple-system, BlinkMacSystemFont, sans-serif; }\n.mac:lang(zh-Hans) { font-family: -apple-system, BlinkMacSystemFont, \"PingFang SC\", \"Hiragino Sans GB\", sans-serif; }\n.mac:lang(zh-Hant) { font-family: -apple-system, BlinkMacSystemFont, \"PingFang TC\", sans-serif; }\n.mac:lang(ja) { font-family: -apple-system, BlinkMacSystemFont, \"Hiragino Kaku Gothic Pro\", sans-serif; }\n.mac:lang(ko) { font-family: -apple-system, BlinkMacSystemFont, \"Apple SD Gothic Neo\", \"Nanum Gothic\", \"AppleGothic\", sans-serif; }\n\n.windows { font-family: \"Segoe WPC\", \"Segoe UI\", sans-serif; }\n.windows:lang(zh-Hans) { font-family: \"Segoe WPC\", \"Segoe UI\", \"Microsoft YaHei\", sans-serif; }\n.windows:lang(zh-Hant) { font-family: \"Segoe WPC\", \"Segoe UI\", \"Microsoft Jhenghei\", sans-serif; }\n.windows:lang(ja) { font-family: \"Segoe WPC\", \"Segoe UI\", \"Yu Gothic UI\", \"Meiryo UI\", sans-serif; }\n.windows:lang(ko) { font-family: \"Segoe WPC\", \"Segoe UI\", \"Malgun Gothic\", \"Dotom\", sans-serif; }\n\n/* Linux: add `system-ui` as first font and not `Ubuntu` to allow other distribution pick their standard OS font */\n.linux { font-family: system-ui, \"Ubuntu\", \"Droid Sans\", sans-serif; }\n.linux:lang(zh-Hans) { font-family: system-ui, \"Ubuntu\", \"Droid Sans\", \"Source Han Sans SC\", \"Source Han Sans CN\", \"Source Han Sans\", sans-serif; }\n.linux:lang(zh-Hant) { font-family: system-ui, \"Ubuntu\", \"Droid Sans\", \"Source Han Sans TC\", \"Source Han Sans TW\", \"Source Han Sans\", sans-serif; }\n.linux:lang(ja) { font-family: system-ui, \"Ubuntu\", \"Droid Sans\", \"Source Han Sans J\", \"Source Han Sans JP\", \"Source Han Sans\", sans-serif; }\n.linux:lang(ko) { font-family: system-ui, \"Ubuntu\", \"Droid Sans\", \"Source Han Sans K\", \"Source Han Sans JR\", \"Source Han Sans\", \"UnDotum\", \"FBaekmuk Gulim\", sans-serif; }\n\nbody {\n\tmargin: 0;\n\tpadding: 0;\n\theight: 100%;\n\twidth: 100%;\n\tuser-select: none;\n\tcolor: #cccccc;\n}\n\n.cpu {\n\twidth: 60px;\n}\n\n.pid {\n\twidth: 50px\n}\n\n.memory {\n\twidth: 90px;\n}\n\n.monaco-list:focus {\n\toutline: 0;\n}\n\n.monaco-list-row:first-of-type {\n\tborder-bottom: 1px solid;\n}\n\n.row {\n\tdisplay: flex;\n}\n\n.centered {\n\ttext-align: center;\n}\n\n.nameLabel{\n\ttext-align: left;\n\twidth: calc(100% - 185px);\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n}\n\n.data {\n\twhite-space: pre;\n\tpadding-left: .5rem;\n\tfont-weight: normal;\n\ttext-align: left;\n\theight: 22px;\n}\n\n.error {\n\tpadding-left: 20px;\n\twhite-space: nowrap;\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n@font-face {\n\tfont-family: \"codicon\";\n\tfont-display: block;\n\tsrc: url(\"./codicon.ttf?5d4d76ab2ce5108968ad644d591a16a6\") format(\"truetype\");\n}\n\n.codicon[class*='codicon-'] {\n\tfont: normal normal normal 16px/1 codicon;\n\tdisplay: inline-block;\n\ttext-decoration: none;\n\ttext-rendering: auto;\n\ttext-align: center;\n\ttext-transform: none;\n\t-webkit-font-smoothing: antialiased;\n\t-moz-osx-font-smoothing: grayscale;\n\tuser-select: none;\n\t-webkit-user-select: none;\n}\n\n/* icon rules are dynamically created by the platform theme service (see iconsStyleSheet.ts) */\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n.codicon-wrench-subaction {\n\topacity: 0.5;\n}\n\n@keyframes codicon-spin {\n\t100% {\n\t\ttransform:rotate(360deg);\n\t}\n}\n\n.codicon-sync.codicon-modifier-spin,\n.codicon-loading.codicon-modifier-spin,\n.codicon-gear.codicon-modifier-spin,\n.codicon-notebook-state-executing.codicon-modifier-spin {\n\t/* Use steps to throttle FPS to reduce CPU usage */\n\tanimation: codicon-spin 1.5s steps(30) infinite;\n}\n\n.codicon-modifier-disabled {\n\topacity: 0.4;\n}\n\n/* custom speed & easing for loading icon */\n.codicon-loading,\n.codicon-tree-item-loading::before {\n\tanimation-duration: 1s !important;\n\tanimation-timing-function: cubic-bezier(0.53, 0.21, 0.29, 0.67) !important;\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n.monaco-aria-container {\n\tposition: absolute; /* try to hide from window but not from screen readers */\n\tleft:-999em;\n}", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n.monaco-list {\n\tposition: relative;\n\theight: 100%;\n\twidth: 100%;\n\twhite-space: nowrap;\n}\n\n.monaco-list.mouse-support {\n\tuser-select: none;\n\t-webkit-user-select: none;\n}\n\n.monaco-list > .monaco-scrollable-element {\n\theight: 100%;\n}\n\n.monaco-list-rows {\n\tposition: relative;\n\twidth: 100%;\n\theight: 100%;\n}\n\n.monaco-list.horizontal-scrolling .monaco-list-rows {\n\twidth: auto;\n\tmin-width: 100%;\n}\n\n.monaco-list-row {\n\tposition: absolute;\n\tbox-sizing: border-box;\n\toverflow: hidden;\n\twidth: 100%;\n}\n\n.monaco-list.mouse-support .monaco-list-row {\n\tcursor: pointer;\n\ttouch-action: none;\n}\n\n/* Make sure the scrollbar renders above overlays (sticky scroll) */\n.monaco-list .monaco-scrollable-element > .scrollbar.vertical,\n.monaco-pane-view > .monaco-split-view2.vertical > .monaco-scrollable-element > .scrollbar.vertical {\n\tz-index: 14;\n}\n\n/* for OS X ballistic scrolling */\n.monaco-list-row.scrolling {\n\tdisplay: none !important;\n}\n\n/* Focus */\n.monaco-list.element-focused,\n.monaco-list.selection-single,\n.monaco-list.selection-multiple {\n\toutline: 0 !important;\n}\n\n/* Dnd */\n.monaco-drag-image {\n\tdisplay: inline-block;\n\tpadding: 1px 7px;\n\tborder-radius: 10px;\n\tfont-size: 12px;\n\tposition: absolute;\n\tz-index: 1000;\n}\n\n/* Filter */\n\n.monaco-list-type-filter-message {\n\tposition: absolute;\n\tbox-sizing: border-box;\n\twidth: 100%;\n\theight: 100%;\n\ttop: 0;\n\tleft: 0;\n\tpadding: 40px 1em 1em 1em;\n\ttext-align: center;\n\twhite-space: normal;\n\topacity: 0.7;\n\tpointer-events: none;\n}\n\n.monaco-list-type-filter-message:empty {\n\tdisplay: none;\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n/* Arrows */\n.monaco-scrollable-element > .scrollbar > .scra {\n\tcursor: pointer;\n\tfont-size: 11px !important;\n}\n\n.monaco-scrollable-element > .visible {\n\topacity: 1;\n\n\t/* Background rule added for IE9 - to allow clicks on dom node */\n\tbackground:rgba(0,0,0,0);\n\n\ttransition: opacity 100ms linear;\n\t/* In front of peek view */\n\tz-index: 11;\n}\n.monaco-scrollable-element > .invisible {\n\topacity: 0;\n\tpointer-events: none;\n}\n.monaco-scrollable-element > .invisible.fade {\n\ttransition: opacity 800ms linear;\n}\n\n/* Scrollable Content Inset Shadow */\n.monaco-scrollable-element > .shadow {\n\tposition: absolute;\n\tdisplay: none;\n}\n.monaco-scrollable-element > .shadow.top {\n\tdisplay: block;\n\ttop: 0;\n\tleft: 3px;\n\theight: 3px;\n\twidth: 100%;\n\tbox-shadow: var(--vscode-scrollbar-shadow) 0 6px 6px -6px inset;\n}\n.monaco-scrollable-element > .shadow.left {\n\tdisplay: block;\n\ttop: 3px;\n\tleft: 0;\n\theight: 100%;\n\twidth: 3px;\n\tbox-shadow: var(--vscode-scrollbar-shadow) 6px 0 6px -6px inset;\n}\n.monaco-scrollable-element > .shadow.top-left-corner {\n\tdisplay: block;\n\ttop: 0;\n\tleft: 0;\n\theight: 3px;\n\twidth: 3px;\n}\n.monaco-scrollable-element > .shadow.top.left {\n\tbox-shadow: var(--vscode-scrollbar-shadow) 6px 0 6px -6px inset;\n}\n\n.monaco-scrollable-element > .scrollbar > .slider {\n\tbackground: var(--vscode-scrollbarSlider-background);\n}\n\n.monaco-scrollable-element > .scrollbar > .slider:hover {\n\tbackground: var(--vscode-scrollbarSlider-hoverBackground);\n}\n\n.monaco-scrollable-element > .scrollbar > .slider.active {\n\tbackground: var(--vscode-scrollbarSlider-activeBackground);\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n/* Use custom CSS vars to expose padding into parent select for padding calculation */\n.monaco-select-box-dropdown-padding {\n\t--dropdown-padding-top: 1px;\n\t--dropdown-padding-bottom: 1px;\n}\n\n.hc-black .monaco-select-box-dropdown-padding,\n.hc-light .monaco-select-box-dropdown-padding {\n\t--dropdown-padding-top: 3px;\n\t--dropdown-padding-bottom: 4px;\n}\n\n.monaco-select-box-dropdown-container {\n\tdisplay: none;\n\tbox-sizing:\tborder-box;\n}\n\n.monaco-select-box-dropdown-container > .select-box-details-pane > .select-box-description-markdown * {\n\tmargin: 0;\n}\n\n.monaco-select-box-dropdown-container > .select-box-details-pane > .select-box-description-markdown a:focus {\n\toutline: 1px solid -webkit-focus-ring-color;\n\toutline-offset: -1px;\n}\n\n.monaco-select-box-dropdown-container > .select-box-details-pane > .select-box-description-markdown code {\n\tline-height: 15px; /** For some reason, this is needed, otherwise <code> will take up 20px height */\n\tfont-family: var(--monaco-monospace-font);\n}\n\n\n.monaco-select-box-dropdown-container.visible {\n\tdisplay: flex;\n\tflex-direction: column;\n\ttext-align: left;\n\twidth: 1px;\n\toverflow: hidden;\n\tborder-bottom-left-radius: 3px;\n\tborder-bottom-right-radius: 3px;\n}\n\n.monaco-select-box-dropdown-container > .select-box-dropdown-list-container {\n\tflex: 0 0 auto;\n\talign-self: flex-start;\n\tpadding-top: var(--dropdown-padding-top);\n\tpadding-bottom: var(--dropdown-padding-bottom);\n\tpadding-left: 1px;\n\tpadding-right: 1px;\n\twidth: 100%;\n\toverflow: hidden;\n\tbox-sizing:\tborder-box;\n}\n\n.monaco-select-box-dropdown-container > .select-box-details-pane {\n\tpadding: 5px;\n}\n\n.hc-black .monaco-select-box-dropdown-container > .select-box-dropdown-list-container {\n\tpadding-top: var(--dropdown-padding-top);\n\tpadding-bottom: var(--dropdown-padding-bottom);\n}\n\n.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row {\n\tcursor: pointer;\n}\n\n.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row > .option-text {\n\ttext-overflow: ellipsis;\n\toverflow: hidden;\n\tpadding-left: 3.5px;\n\twhite-space: nowrap;\n\tfloat: left;\n}\n\n.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row > .option-detail {\n\ttext-overflow: ellipsis;\n\toverflow: hidden;\n\tpadding-left: 3.5px;\n\twhite-space: nowrap;\n\tfloat: left;\n\topacity: 0.7;\n}\n\n.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row > .option-decorator-right {\n\ttext-overflow: ellipsis;\n\toverflow: hidden;\n\tpadding-right: 10px;\n\twhite-space: nowrap;\n\tfloat: right;\n}\n\n\n/* Accepted CSS hiding technique for accessibility reader text  */\n/* https://webaim.org/techniques/css/invisiblecontent/ */\n\n.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row > .visually-hidden {\n\t\tposition: absolute;\n\t\tleft: -10000px;\n\t\ttop: auto;\n\t\twidth: 1px;\n\t\theight: 1px;\n\t\toverflow: hidden;\n}\n\n.monaco-select-box-dropdown-container > .select-box-dropdown-container-width-control {\n\tflex: 1 1 auto;\n\talign-self: flex-start;\n\topacity: 0;\n}\n\n.monaco-select-box-dropdown-container > .select-box-dropdown-container-width-control > .width-control-div {\n\toverflow: hidden;\n\tmax-height: 0px;\n}\n\n.monaco-select-box-dropdown-container > .select-box-dropdown-container-width-control > .width-control-div > .option-text-width-control {\n\tpadding-left: 4px;\n\tpadding-right: 8px;\n\twhite-space: nowrap;\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n.monaco-select-box {\n\twidth: 100%;\n\tcursor: pointer;\n\tborder-radius: 2px;\n}\n\n.monaco-select-box-dropdown-container {\n\tfont-size: 13px;\n\tfont-weight: normal;\n\ttext-transform: none;\n}\n\n/** Actions */\n\n.monaco-action-bar .action-item.select-container {\n\tcursor: default;\n}\n\n.monaco-action-bar .action-item .monaco-select-box {\n\tcursor: pointer;\n\tmin-width: 100px;\n\tmin-height: 18px;\n\tpadding: 2px 23px 2px 8px;\n}\n\n.mac .monaco-action-bar .action-item .monaco-select-box {\n\tfont-size: 11px;\n\tborder-radius: 5px;\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n.monaco-action-bar {\n\twhite-space: nowrap;\n\theight: 100%;\n}\n\n.monaco-action-bar .actions-container {\n\tdisplay: flex;\n\tmargin: 0 auto;\n\tpadding: 0;\n\theight: 100%;\n\twidth: 100%;\n\talign-items: center;\n}\n\n.monaco-action-bar.vertical .actions-container {\n\tdisplay: inline-block;\n}\n\n.monaco-action-bar .action-item {\n\tdisplay: block;\n\talign-items: center;\n\tjustify-content: center;\n\tcursor: pointer;\n\tposition: relative;  /* DO NOT REMOVE - this is the key to preventing the ghosting icon bug in Chrome 42 */\n}\n\n.monaco-action-bar .action-item.disabled {\n\tcursor: default;\n}\n\n.monaco-action-bar .action-item .icon,\n.monaco-action-bar .action-item .codicon {\n\tdisplay: block;\n}\n\n.monaco-action-bar .action-item .codicon {\n\tdisplay: flex;\n\talign-items: center;\n\twidth: 16px;\n\theight: 16px;\n}\n\n.monaco-action-bar .action-label {\n\tdisplay: flex;\n\tfont-size: 11px;\n\tpadding: 3px;\n\tborder-radius: 5px;\n}\n\n.monaco-action-bar .action-item.disabled .action-label:not(.icon) ,\n.monaco-action-bar .action-item.disabled .action-label:not(.icon)::before,\n.monaco-action-bar .action-item.disabled .action-label:not(.icon):hover {\n\tcolor: var(--vscode-disabledForeground);\n}\n\n/* Unable to change color of SVGs, hence opacity is used */\n.monaco-action-bar .action-item.disabled .action-label.icon ,\n.monaco-action-bar .action-item.disabled .action-label.icon::before,\n.monaco-action-bar .action-item.disabled .action-label.icon:hover {\n\topacity: 0.6;\n}\n\n/* Vertical actions */\n\n.monaco-action-bar.vertical {\n\ttext-align: left;\n}\n\n.monaco-action-bar.vertical .action-item {\n\tdisplay: block;\n}\n\n.monaco-action-bar.vertical .action-label.separator {\n\tdisplay: block;\n\tborder-bottom: 1px solid #bbb;\n\tpadding-top: 1px;\n\tmargin-left: .8em;\n\tmargin-right: .8em;\n}\n\n.monaco-action-bar .action-item .action-label.separator {\n\twidth: 1px;\n\theight: 16px;\n\tmargin: 5px 4px !important;\n\tcursor: default;\n\tmin-width: 1px;\n\tpadding: 0;\n\tbackground-color: #bbb;\n}\n\n.secondary-actions .monaco-action-bar .action-label {\n\tmargin-left: 6px;\n}\n\n/* Action Items */\n.monaco-action-bar .action-item.select-container {\n\toverflow: hidden; /* somehow the dropdown overflows its container, we prevent it here to not push */\n\tflex: 1;\n\tmax-width: 170px;\n\tmin-width: 60px;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 10px;\n}\n\n.monaco-action-bar .action-item.action-dropdown-item {\n\tdisplay: flex;\n}\n\n.monaco-action-bar .action-item.action-dropdown-item > .action-dropdown-item-separator {\n\tdisplay: flex;\n\talign-items: center;\n\tcursor: default;\n}\n\n.monaco-action-bar .action-item.action-dropdown-item > .action-dropdown-item-separator > div {\n\twidth: 1px;\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n.monaco-custom-toggle {\n\tmargin-left: 2px;\n\tfloat: left;\n\tcursor: pointer;\n\toverflow: hidden;\n\twidth: 20px;\n\theight: 20px;\n\tborder-radius: 3px;\n\tborder: 1px solid transparent;\n\tpadding: 1px;\n\tbox-sizing:\tborder-box;\n\tuser-select: none;\n\t-webkit-user-select: none;\n}\n\n.monaco-custom-toggle:hover {\n\tbackground-color: var(--vscode-inputOption-hoverBackground);\n}\n\n.hc-black .monaco-custom-toggle:hover,\n.hc-light .monaco-custom-toggle:hover {\n\tborder: 1px dashed var(--vscode-focusBorder);\n}\n\n.hc-black .monaco-custom-toggle,\n.hc-light .monaco-custom-toggle {\n\tbackground: none;\n}\n\n.hc-black .monaco-custom-toggle:hover,\n.hc-light .monaco-custom-toggle:hover {\n\tbackground: none;\n}\n\n.monaco-custom-toggle.monaco-checkbox {\n\theight: 18px;\n\twidth: 18px;\n\tborder: 1px solid transparent;\n\tborder-radius: 3px;\n\tmargin-right: 9px;\n\tmargin-left: 0px;\n\tpadding: 0px;\n\topacity: 1;\n\tbackground-size: 16px !important;\n}\n\n.monaco-action-bar .checkbox-action-item {\n\tdisplay: flex;\n\talign-items: center;\n\tborder-radius: 2px;\n\tpadding-right: 2px;\n}\n\n.monaco-action-bar .checkbox-action-item:hover {\n\tbackground-color: var(--vscode-toolbar-hoverBackground);\n}\n\n.monaco-action-bar .checkbox-action-item > .monaco-custom-toggle.monaco-checkbox {\n\tmargin-right: 4px;\n}\n\n.monaco-action-bar .checkbox-action-item > .checkbox-label {\n\tfont-size: 12px;\n}\n\n/* hide check when unchecked */\n.monaco-custom-toggle.monaco-checkbox:not(.checked)::before {\n\tvisibility: hidden;\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n.monaco-inputbox {\n\tposition: relative;\n\tdisplay: block;\n\tpadding: 0;\n\tbox-sizing:\tborder-box;\n\tborder-radius: 2px;\n\n\t/* Customizable */\n\tfont-size: inherit;\n}\n\n.monaco-inputbox > .ibwrapper > .input,\n.monaco-inputbox > .ibwrapper > .mirror {\n\n\t/* Customizable */\n\tpadding: 4px 6px;\n}\n\n.monaco-inputbox > .ibwrapper {\n\tposition: relative;\n\twidth: 100%;\n}\n\n.monaco-inputbox > .ibwrapper > .input {\n\tdisplay: inline-block;\n\tbox-sizing:\tborder-box;\n\twidth: 100%;\n\theight: 100%;\n\tline-height: inherit;\n\tborder: none;\n\tfont-family: inherit;\n\tfont-size: inherit;\n\tresize: none;\n\tcolor: inherit;\n}\n\n.monaco-inputbox > .ibwrapper > input {\n\ttext-overflow: ellipsis;\n}\n\n.monaco-inputbox > .ibwrapper > textarea.input {\n\tdisplay: block;\n\tscrollbar-width: none; /* Firefox: hide scrollbars */\n\toutline: none;\n}\n\n.monaco-inputbox > .ibwrapper > textarea.input::-webkit-scrollbar {\n\tdisplay: none; /* Chrome + Safari: hide scrollbar */\n}\n\n.monaco-inputbox > .ibwrapper > textarea.input.empty {\n\twhite-space: nowrap;\n}\n\n.monaco-inputbox > .ibwrapper > .mirror {\n\tposition: absolute;\n\tdisplay: inline-block;\n\twidth: 100%;\n\ttop: 0;\n\tleft: 0;\n\tbox-sizing: border-box;\n\twhite-space: pre-wrap;\n\tvisibility: hidden;\n\tword-wrap: break-word;\n}\n\n/* Context view */\n\n.monaco-inputbox-container {\n\ttext-align: right;\n}\n\n.monaco-inputbox-container .monaco-inputbox-message {\n\tdisplay: inline-block;\n\toverflow: hidden;\n\ttext-align: left;\n\twidth: 100%;\n\tbox-sizing:\tborder-box;\n\tpadding: 0.4em;\n\tfont-size: 12px;\n\tline-height: 17px;\n\tmargin-top: -1px;\n\tword-wrap: break-word;\n}\n\n/* Action bar support */\n.monaco-inputbox .monaco-action-bar {\n\tposition: absolute;\n\tright: 2px;\n\ttop: 4px;\n}\n\n.monaco-inputbox .monaco-action-bar .action-item {\n\tmargin-left: 2px;\n}\n\n.monaco-inputbox .monaco-action-bar .action-item .codicon {\n\tbackground-repeat: no-repeat;\n\twidth: 16px;\n\theight: 16px;\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n/* ---------- Find input ---------- */\n\n.monaco-findInput {\n\tposition: relative;\n}\n\n.monaco-findInput .monaco-inputbox {\n\tfont-size: 13px;\n\twidth: 100%;\n}\n\n.monaco-findInput > .controls {\n\tposition: absolute;\n\ttop: 3px;\n\tright: 2px;\n}\n\n.vs .monaco-findInput.disabled {\n\tbackground-color: #E1E1E1;\n}\n\n/* Theming */\n.vs-dark .monaco-findInput.disabled {\n\tbackground-color: #333;\n}\n\n/* Highlighting */\n.monaco-findInput.highlight-0 .controls,\n.hc-light .monaco-findInput.highlight-0 .controls {\n\tanimation: monaco-findInput-highlight-0 100ms linear 0s;\n}\n\n.monaco-findInput.highlight-1 .controls,\n.hc-light .monaco-findInput.highlight-1 .controls {\n\tanimation: monaco-findInput-highlight-1 100ms linear 0s;\n}\n\n.hc-black .monaco-findInput.highlight-0 .controls,\n.vs-dark  .monaco-findInput.highlight-0 .controls {\n\tanimation: monaco-findInput-highlight-dark-0 100ms linear 0s;\n}\n\n.hc-black .monaco-findInput.highlight-1 .controls,\n.vs-dark  .monaco-findInput.highlight-1 .controls {\n\tanimation: monaco-findInput-highlight-dark-1 100ms linear 0s;\n}\n\n@keyframes monaco-findInput-highlight-0 {\n\t0% { background: rgba(253, 255, 0, 0.8); }\n\t100% { background: transparent; }\n}\n@keyframes monaco-findInput-highlight-1 {\n\t0% { background: rgba(253, 255, 0, 0.8); }\n\t/* Made intentionally different such that the CSS minifier does not collapse the two animations into a single one*/\n\t99% { background: transparent; }\n}\n\n@keyframes monaco-findInput-highlight-dark-0 {\n\t0% { background: rgba(255, 255, 255, 0.44); }\n\t100% { background: transparent; }\n}\n@keyframes monaco-findInput-highlight-dark-1 {\n\t0% { background: rgba(255, 255, 255, 0.44); }\n\t/* Made intentionally different such that the CSS minifier does not collapse the two animations into a single one*/\n\t99% { background: transparent; }\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n.monaco-tl-row {\n\tdisplay: flex;\n\theight: 100%;\n\talign-items: center;\n\tposition: relative;\n}\n\n.monaco-tl-row.disabled {\n\tcursor: default;\n}\n.monaco-tl-indent {\n\theight: 100%;\n\tposition: absolute;\n\ttop: 0;\n\tleft: 16px;\n\tpointer-events: none;\n}\n\n.hide-arrows .monaco-tl-indent {\n\tleft: 12px;\n}\n\n.monaco-tl-indent > .indent-guide {\n\tdisplay: inline-block;\n\tbox-sizing: border-box;\n\theight: 100%;\n\tborder-left: 1px solid transparent;\n}\n\n.monaco-workbench:not(.reduce-motion) .monaco-tl-indent > .indent-guide {\n\ttransition: border-color 0.1s linear;\n}\n\n.monaco-tl-twistie,\n.monaco-tl-contents {\n\theight: 100%;\n}\n\n.monaco-tl-twistie {\n\tfont-size: 10px;\n\ttext-align: right;\n\tpadding-right: 6px;\n\tflex-shrink: 0;\n\twidth: 16px;\n\tdisplay: flex !important;\n\talign-items: center;\n\tjustify-content: center;\n\ttransform: translateX(3px);\n}\n\n.monaco-tl-contents {\n\tflex: 1;\n\toverflow: hidden;\n}\n\n.monaco-tl-twistie::before {\n\tborder-radius: 20px;\n}\n\n.monaco-tl-twistie.collapsed::before {\n\ttransform: rotate(-90deg);\n}\n\n.monaco-tl-twistie.codicon-tree-item-loading::before {\n\t/* Use steps to throttle FPS to reduce CPU usage */\n\tanimation: codicon-spin 1.25s steps(30) infinite;\n}\n\n.monaco-tree-type-filter {\n\tposition: absolute;\n\ttop: 0;\n\tright: 0;\n\tdisplay: flex;\n\tpadding: 3px;\n\tmax-width: 200px;\n\tz-index: 100;\n\tmargin: 0 10px 0 6px;\n\tborder: 1px solid var(--vscode-widget-border);\n\tborder-bottom-left-radius: 4px;\n\tborder-bottom-right-radius: 4px;\n}\n\n.monaco-workbench:not(.reduce-motion) .monaco-tree-type-filter {\n\ttransition: top 0.3s;\n}\n\n.monaco-tree-type-filter.disabled {\n\ttop: -40px !important;\n}\n\n.monaco-tree-type-filter-input {\n\tflex: 1;\n}\n\n.monaco-tree-type-filter-input .monaco-inputbox {\n\theight: 23px;\n}\n\n.monaco-tree-type-filter-input .monaco-inputbox > .ibwrapper > .input,\n.monaco-tree-type-filter-input .monaco-inputbox > .ibwrapper > .mirror {\n\tpadding: 2px 4px;\n}\n\n.monaco-tree-type-filter-input .monaco-findInput > .controls {\n\ttop: 2px;\n}\n\n.monaco-tree-type-filter-actionbar {\n\tmargin-left: 4px;\n}\n\n.monaco-tree-type-filter-actionbar .monaco-action-bar .action-label {\n\tpadding: 2px;\n}\n\n.monaco-list .monaco-scrollable-element .monaco-tree-sticky-container{\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 0;\n\tz-index: 13; /* Settings editor uses z-index: 12 */\n\n\t/* Backup color in case the tree does not provide the background color */\n\tbackground-color: var(--vscode-sideBar-background);\n}\n\n.monaco-list .monaco-scrollable-element .monaco-tree-sticky-container .monaco-tree-sticky-row.monaco-list-row{\n\tposition: absolute;\n\twidth: 100%;\n\topacity: 1 !important; /* Settings editor uses opacity < 1 */\n\toverflow: hidden;\n\n\t/* Backup color in case the tree does not provide the background color */\n\tbackground-color: var(--vscode-sideBar-background);\n}\n\n.monaco-list .monaco-scrollable-element .monaco-tree-sticky-container .monaco-tree-sticky-row:hover{\n\tbackground-color: var(--vscode-list-hoverBackground) !important;\n\tcursor: pointer;\n}\n\n.monaco-list .monaco-scrollable-element .monaco-tree-sticky-container.empty,\n.monaco-list .monaco-scrollable-element .monaco-tree-sticky-container.empty .monaco-tree-sticky-container-shadow {\n\tdisplay: none;\n}\n\n.monaco-list .monaco-scrollable-element .monaco-tree-sticky-container .monaco-tree-sticky-container-shadow {\n\tposition: absolute;\n\tbottom: -3px;\n\tleft: 0px;\n\theight: 0px; /* heigt is 3px and only set when there is a treeStickyScrollShadow color */\n\twidth: 100%;\n}\n\n.monaco-list .monaco-scrollable-element .monaco-tree-sticky-container[tabindex=\"0\"]:focus{\n\toutline: none;\n}\n"], "mappings": ";;;;AAKA;AACC,aAAW;AACZ;AAIA,CAAC;AAAM;AAAA,IAAa,aAAa;AAAA,IAAE,kBAAkB;AAAA,IAAE;AAAY;AACnE,CADC,GACG,MAAM;AAAW;AAAA,IAAa,aAAa;AAAA,IAAE,kBAAkB;AAAA,IAAE,aAAa;AAAA,IAAE,kBAAkB;AAAA,IAAE;AAAY;AACpH,CAFC,GAEG,MAAM;AAAW;AAAA,IAAa,aAAa;AAAA,IAAE,kBAAkB;AAAA,IAAE,aAAa;AAAA,IAAE;AAAY;AAChG,CAHC,GAGG,MAAM;AAAM;AAAA,IAAa,aAAa;AAAA,IAAE,kBAAkB;AAAA,IAAE,0BAA0B;AAAA,IAAE;AAAY;AACxG,CAJC,GAIG,MAAM;AAAM;AAAA,IAAa,aAAa;AAAA,IAAE,kBAAkB;AAAA,IAAE,qBAAqB;AAAA,IAAE,cAAc;AAAA,IAAE,aAAa;AAAA,IAAE;AAAY;AAElI,CAAC;AAAU;AAAA,IAAa,WAAW;AAAA,IAAE,UAAU;AAAA,IAAE;AAAY;AAC7D,CADC,OACO,MAAM;AAAW;AAAA,IAAa,WAAW;AAAA,IAAE,UAAU;AAAA,IAAE,iBAAiB;AAAA,IAAE;AAAY;AAC9F,CAFC,OAEO,MAAM;AAAW;AAAA,IAAa,WAAW;AAAA,IAAE,UAAU;AAAA,IAAE,oBAAoB;AAAA,IAAE;AAAY;AACjG,CAHC,OAGO,MAAM;AAAM;AAAA,IAAa,WAAW;AAAA,IAAE,UAAU;AAAA,IAAE,cAAc;AAAA,IAAE,WAAW;AAAA,IAAE;AAAY;AACnG,CAJC,OAIO,MAAM;AAAM;AAAA,IAAa,WAAW;AAAA,IAAE,UAAU;AAAA,IAAE,eAAe;AAAA,IAAE,OAAO;AAAA,IAAE;AAAY;AAGhG,CAAC;AAAQ;AAAA,IAAa,SAAS;AAAA,IAAE,QAAQ;AAAA,IAAE,YAAY;AAAA,IAAE;AAAY;AACrE,CADC,KACK,MAAM;AAAW;AAAA,IAAa,SAAS;AAAA,IAAE,QAAQ;AAAA,IAAE,YAAY;AAAA,IAAE,oBAAoB;AAAA,IAAE,oBAAoB;AAAA,IAAE,iBAAiB;AAAA,IAAE;AAAY;AAClJ,CAFC,KAEK,MAAM;AAAW;AAAA,IAAa,SAAS;AAAA,IAAE,QAAQ;AAAA,IAAE,YAAY;AAAA,IAAE,oBAAoB;AAAA,IAAE,oBAAoB;AAAA,IAAE,iBAAiB;AAAA,IAAE;AAAY;AAClJ,CAHC,KAGK,MAAM;AAAM;AAAA,IAAa,SAAS;AAAA,IAAE,QAAQ;AAAA,IAAE,YAAY;AAAA,IAAE,mBAAmB;AAAA,IAAE,oBAAoB;AAAA,IAAE,iBAAiB;AAAA,IAAE;AAAY;AAC5I,CAJC,KAIK,MAAM;AAAM;AAAA,IAAa,SAAS;AAAA,IAAE,QAAQ;AAAA,IAAE,YAAY;AAAA,IAAE,mBAAmB;AAAA,IAAE,oBAAoB;AAAA,IAAE,iBAAiB;AAAA,IAAE,SAAS;AAAA,IAAE,gBAAgB;AAAA,IAAE;AAAY;AAEzK;AACC,UAAQ;AACR,WAAS;AACT,UAAQ;AACR,SAAO;AACP,eAAa;AACb,SAAO;AACR;AAEA,CAAC;AACA,SAAO;AACR;AAEA,CAAC;AACA,SAAO;AACR;AAEA,CAAC;AACA,SAAO;AACR;AAEA,CAAC,WAAW;AACX,WAAS;AACV;AAEA,CAAC,eAAe;AACf,iBAAe,IAAI;AACpB;AAEA,CAAC;AACA,WAAS;AACV;AAEA,CAAC;AACA,cAAY;AACb;AAEA,CAAC;AACA,cAAY;AACZ,SAAO,KAAK,KAAK,EAAE;AACnB,YAAU;AACV,iBAAe;AAChB;AAEA,CAAC;AACA,eAAa;AACb,gBAAc;AACd,eAAa;AACb,cAAY;AACZ,UAAQ;AACT;AAEA,CAAC;AACA,gBAAc;AACd,eAAa;AACd;;;AChFA;AACC,eAAa;AACb,gBAAc;AACd,OAAK,sEAAsD,OAAO;AACnE;AAEA,CAAC,OAAO,CAAC;AACR,QAAM,OAAO,OAAO,OAAO,IAAI,CAAC,EAAE;AAClC,WAAS;AACT,mBAAiB;AACjB,kBAAgB;AAChB,cAAY;AACZ,kBAAgB;AAChB,0BAAwB;AACxB,2BAAyB;AACzB,eAAa;AACb,uBAAqB;AACtB;;;ACjBA,CAAC;AACA,WAAS;AACV;AAEA,WAAW;AACV;AACC,eAAU,OAAO;AAClB;AACD;AAEA,CAAC,YAAY,CAAC;AACd,CAAC,eAAe,CADF;AAEd,CAAC,YAAY,CAFC;AAGd,CAAC,gCAAgC,CAHnB;AAKb,aAAW,aAAa,KAAK,MAAM,IAAI;AACxC;AAEA,CAAC;AACA,WAAS;AACV;AAGA,CAZC;AAaD,CAAC,yBAAyB;AACzB,sBAAoB;AACpB,6BAA2B,aAAa,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAC3D;;;AC3BA,CAAC;AACA,YAAU;AACV,QAAK;AACN;;;ACHA,CAAC;AACA,YAAU;AACV,UAAQ;AACR,SAAO;AACP,eAAa;AACd;AAEA,CAPC,WAOW,CAAC;AACZ,eAAa;AACb,uBAAqB;AACtB;AAEA,CAZC,YAYY,EAAE,CAAC;AACf,UAAQ;AACT;AAEA,CAAC;AACA,YAAU;AACV,SAAO;AACP,UAAQ;AACT;AAEA,CAtBC,WAsBW,CAAC,qBAAqB,CANjC;AAOA,SAAO;AACP,aAAW;AACZ;AAEA,CAAC;AACA,YAAU;AACV,cAAY;AACZ,YAAU;AACV,SAAO;AACR;AAEA,CAlCC,WAkCW,CA3BC,cA2Bc,CAP1B;AAQA,UAAQ;AACR,gBAAc;AACf;AAGA,CAxCC,YAwCY,CA5BG,0BA4BwB,EAAE,CAAC,SAAS,CAAC;AACrD,CAAC,iBAAiB,EAAE,CAAC,kBAAkB,CADc,SACJ,EAAE,CA7BnC,0BA6B8D,EAAE,CADrC,SAC+C,CADrC;AAEpD,WAAS;AACV;AAGA,CAnBC,eAmBe,CAAC;AAChB,WAAS;AACV;AAGA,CAnDC,WAmDW,CAAC;AACb,CApDC,WAoDW,CAAC;AACb,CArDC,WAqDW,CAAC;AACZ,WAAS;AACV;AAGA,CAAC;AACA,WAAS;AACT,WAAS,IAAI;AACb,iBAAe;AACf,aAAW;AACX,YAAU;AACV,WAAS;AACV;AAIA,CAAC;AACA,YAAU;AACV,cAAY;AACZ,SAAO;AACP,UAAQ;AACR,OAAK;AACL,QAAM;AACN,WAAS,KAAK,IAAI,IAAI;AACtB,cAAY;AACZ,eAAa;AACb,WAAS;AACT,kBAAgB;AACjB;AAEA,CAdC,+BAc+B;AAC/B,WAAS;AACV;;;ACpFA,CAAC,0BAA0B,EAAE,CAAC,UAAU,EAAE,CAAC;AAC1C,UAAQ;AACR,aAAW;AACZ;AAEA,CALC,0BAK0B,EAAE,CAAC;AAC7B,WAAS;AAGT,cAAW,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;AAEtB,cAAY,QAAQ,MAAM;AAE1B,WAAS;AACV;AACA,CAfC,0BAe0B,EAAE,CAAC;AAC7B,WAAS;AACT,kBAAgB;AACjB;AACA,CAnBC,0BAmB0B,EAAE,CAJC,SAIS,CAAC;AACvC,cAAY,QAAQ,MAAM;AAC3B;AAGA,CAxBC,0BAwB0B,EAAE,CAAC;AAC7B,YAAU;AACV,WAAS;AACV;AACA,CA5BC,0BA4B0B,EAAE,CAJC,MAIM,CAAC;AACpC,WAAS;AACT,OAAK;AACL,QAAM;AACN,UAAQ;AACR,SAAO;AACP,cAAY,IAAI,2BAA2B,EAAE,IAAI,IAAI,KAAK;AAC3D;AACA,CApCC,0BAoC0B,EAAE,CAZC,MAYM,CAAC;AACpC,WAAS;AACT,OAAK;AACL,QAAM;AACN,UAAQ;AACR,SAAO;AACP,cAAY,IAAI,2BAA2B,IAAI,EAAE,IAAI,KAAK;AAC3D;AACA,CA5CC,0BA4C0B,EAAE,CApBC,MAoBM,CAAC;AACpC,WAAS;AACT,OAAK;AACL,QAAM;AACN,UAAQ;AACR,SAAO;AACR;AACA,CAnDC,0BAmD0B,EAAE,CA3BC,MA2BM,CAvBC,GAuBG,CAfH;AAgBpC,cAAY,IAAI,2BAA2B,IAAI,EAAE,IAAI,KAAK;AAC3D;AAEA,CAvDC,0BAuD0B,EAAE,CAvDC,UAuDU,EAAE,CAAC;AAC1C,cAAY,IAAI;AACjB;AAEA,CA3DC,0BA2D0B,EAAE,CA3DC,UA2DU,EAAE,CAJC,MAIM;AAChD,cAAY,IAAI;AACjB;AAEA,CA/DC,0BA+D0B,EAAE,CA/DC,UA+DU,EAAE,CARC,MAQM,CAAC;AACjD,cAAY,IAAI;AACjB;;;ACjEA,CAAC;AACA,0BAAwB;AACxB,6BAA2B;AAC5B;AAEA,CAAC,SAAS,CALT;AAMD,CAAC,SAAS,CANT;AAOA,0BAAwB;AACxB,6BAA2B;AAC5B;AAEA,CAAC;AACA,WAAS;AACT,cAAY;AACb;AAEA,CALC,qCAKqC,EAAE,CAAC,wBAAwB,EAAE,CAAC,gCAAgC;AACnG,UAAQ;AACT;AAEA,CATC,qCASqC,EAAE,CAJC,wBAIwB,EAAE,CAJC,gCAIgC,CAAC;AACpG,WAAS,IAAI,MAAM;AACnB,kBAAgB;AACjB;AAEA,CAdC,qCAcqC,EAAE,CATC,wBASwB,EAAE,CATC,gCASgC;AACnG,eAAa;AACb,eAAa,IAAI;AAClB;AAGA,CApBC,oCAoBoC,CAAC;AACrC,WAAS;AACT,kBAAgB;AAChB,cAAY;AACZ,SAAO;AACP,YAAU;AACV,6BAA2B;AAC3B,8BAA4B;AAC7B;AAEA,CA9BC,qCA8BqC,EAAE,CAAC;AACxC,QAAM,EAAE,EAAE;AACV,cAAY;AACZ,eAAa,IAAI;AACjB,kBAAgB,IAAI;AACpB,gBAAc;AACd,iBAAe;AACf,SAAO;AACP,YAAU;AACV,cAAY;AACb;AAEA,CA1CC,qCA0CqC,EAAE,CArCC;AAsCxC,WAAS;AACV;AAEA,CApDC,SAoDS,CA9CT,qCA8C+C,EAAE,CAhBT;AAiBxC,eAAa,IAAI;AACjB,kBAAgB,IAAI;AACrB;AAEA,CAnDC,qCAmDqC,EAAE,CArBC,mCAqBmC,CAAC,YAAY,CAAC;AACzF,UAAQ;AACT;AAEA,CAvDC,qCAuDqC,EAAE,CAzBC,mCAyBmC,CAJC,YAIY,CAJC,gBAIgB,EAAE,CAAC;AAC5G,iBAAe;AACf,YAAU;AACV,gBAAc;AACd,eAAa;AACb,SAAO;AACR;AAEA,CA/DC,qCA+DqC,EAAE,CAjCC,mCAiCmC,CAZC,YAYY,CAZC,gBAYgB,EAAE,CAAC;AAC5G,iBAAe;AACf,YAAU;AACV,gBAAc;AACd,eAAa;AACb,SAAO;AACP,WAAS;AACV;AAEA,CAxEC,qCAwEqC,EAAE,CA1CC,mCA0CmC,CArBC,YAqBY,CArBC,gBAqBgB,EAAE,CAAC;AAC5G,iBAAe;AACf,YAAU;AACV,iBAAe;AACf,eAAa;AACb,SAAO;AACR;AAMA,CApFC,qCAoFqC,EAAE,CAtDC,mCAsDmC,CAjCC,YAiCY,CAjCC,gBAiCgB,EAAE,CAAC;AAC3G,YAAU;AACV,QAAM;AACN,OAAK;AACL,SAAO;AACP,UAAQ;AACR,YAAU;AACZ;AAEA,CA7FC,qCA6FqC,EAAE,CAAC;AACxC,QAAM,EAAE,EAAE;AACV,cAAY;AACZ,WAAS;AACV;AAEA,CAnGC,qCAmGqC,EAAE,CANC,4CAM4C,EAAE,CAAC;AACvF,YAAU;AACV,cAAY;AACb;AAEA,CAxGC,qCAwGqC,EAAE,CAXC,4CAW4C,EAAE,CALC,kBAKkB,EAAE,CAAC;AAC5G,gBAAc;AACd,iBAAe;AACf,eAAa;AACd;;;ACxHA,CAAC;AACA,SAAO;AACP,UAAQ;AACR,iBAAe;AAChB;AAEA,CAAC;AACA,aAAW;AACX,eAAa;AACb,kBAAgB;AACjB;AAIA,CAAC,kBAAkB,CAAC,WAAW,CAAC;AAC/B,UAAQ;AACT;AAEA,CAJC,kBAIkB,CAJC,YAIY,CAlB/B;AAmBA,UAAQ;AACR,aAAW;AACX,cAAY;AACZ,WAAS,IAAI,KAAK,IAAI;AACvB;AAEA,CAAC,IAAI,CAXJ,kBAWuB,CAXJ,YAWiB,CAzBpC;AA0BA,aAAW;AACX,iBAAe;AAChB;;;AC5BA,CAAC;AACA,eAAa;AACb,UAAQ;AACT;AAEA,CALC,kBAKkB,CAAC;AACnB,WAAS;AACT,UAAQ,EAAE;AACV,WAAS;AACT,UAAQ;AACR,SAAO;AACP,eAAa;AACd;AAEA,CAdC,iBAciB,CAAC,SAAS,CATR;AAUnB,WAAS;AACV;AAEA,CAlBC,kBAkBkB,CAAC;AACnB,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,UAAQ;AACR,YAAU;AACX;AAEA,CA1BC,kBA0BkB,CARC,WAQW,CAAC;AAC/B,UAAQ;AACT;AAEA,CA9BC,kBA8BkB,CAZC,YAYY,CAAC;AACjC,CA/BC,kBA+BkB,CAbC,YAaY,CAAC;AAChC,WAAS;AACV;AAEA,CAnCC,kBAmCkB,CAjBC,YAiBY,CAJC;AAKhC,WAAS;AACT,eAAa;AACb,SAAO;AACP,UAAQ;AACT;AAEA,CA1CC,kBA0CkB,CAAC;AACnB,WAAS;AACT,aAAW;AACX,WAAS;AACT,iBAAe;AAChB;AAEA,CAjDC,kBAiDkB,CA/BC,WA+BW,CAvBC,SAuBS,CAPrB,YAOkC,KAAK,CAnB1B;AAoBjC,CAlDC,kBAkDkB,CAhCC,WAgCW,CAxBC,SAwBS,CARrB,YAQkC,KAAK,CApB1B,KAoBgC;AACjE,CAnDC,kBAmDkB,CAjCC,WAiCW,CAzBC,SAyBS,CATrB,YASkC,KAAK,CArB1B,KAqBgC;AAChE,SAAO,IAAI;AACZ;AAGA,CAxDC,kBAwDkB,CAtCC,WAsCW,CA9BC,SA8BS,CAdrB,YAckC,CA1BrB;AA2BjC,CAzDC,kBAyDkB,CAvCC,WAuCW,CA/BC,SA+BS,CAfrB,YAekC,CA3BrB,IA2B0B;AAC3D,CA1DC,kBA0DkB,CAxCC,WAwCW,CAhCC,SAgCS,CAhBrB,YAgBkC,CA5BrB,IA4B0B;AAC1D,WAAS;AACV;AAIA,CAhEC,iBAgEiB,CAlDC;AAmDlB,cAAY;AACb;AAEA,CApEC,iBAoEiB,CAtDC,SAsDS,CAlDR;AAmDnB,WAAS;AACV;AAEA,CAxEC,iBAwEiB,CA1DC,SA0DS,CA9BR,YA8BqB,CAAC;AACzC,WAAS;AACT,iBAAe,IAAI,MAAM;AACzB,eAAa;AACb,eAAa;AACb,gBAAc;AACf;AAEA,CAhFC,kBAgFkB,CA9DC,YA8DY,CAtCZ,YAsCyB,CARH;AASzC,SAAO;AACP,UAAQ;AACR,UAAQ,IAAI;AACZ,UAAQ;AACR,aAAW;AACX,WAAS;AACT,oBAAkB;AACnB;AAEA,CAAC,kBAAkB,CA1FlB,kBA0FqC,CAhDlB;AAiDnB,eAAa;AACd;AAGA,CA/FC,kBA+FkB,CA7EC,WA6EW,CAAC;AAC/B,YAAU;AACV,QAAM;AACN,aAAW;AACX,aAAW;AACX,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,gBAAc;AACf;AAEA,CA1GC,kBA0GkB,CAxFC,WAwFW,CAAC;AAC/B,WAAS;AACV;AAEA,CA9GC,kBA8GkB,CA5FC,WA4FW,CAJC,qBAIqB,EAAE,CAAC;AACvD,WAAS;AACT,eAAa;AACb,UAAQ;AACT;AAEA,CApHC,kBAoHkB,CAlGC,WAkGW,CAVC,qBAUqB,EAAE,CANC,+BAM+B,EAAE;AACxF,SAAO;AACR;;;ACtHA,CAAC;AACA,eAAa;AACb,SAAO;AACP,UAAQ;AACR,YAAU;AACV,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,UAAQ,IAAI,MAAM;AAClB,WAAS;AACT,cAAY;AACZ,eAAa;AACb,uBAAqB;AACtB;AAEA,CAfC,oBAeoB;AACpB,oBAAkB,IAAI;AACvB;AAEA,CAAC,SAAS,CAnBT,oBAmB8B;AAC/B,CAAC,SAAS,CApBT,oBAoB8B;AAC9B,UAAQ,IAAI,OAAO,IAAI;AACxB;AAEA,CALC,SAKS,CAxBT;AAyBD,CALC,SAKS,CAzBT;AA0BA,cAAY;AACb;AAEA,CAVC,SAUS,CA7BT,oBA6B8B;AAC/B,CAVC,SAUS,CA9BT,oBA8B8B;AAC9B,cAAY;AACb;AAEA,CAlCC,oBAkCoB,CAAC;AACrB,UAAQ;AACR,SAAO;AACP,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,gBAAc;AACd,eAAa;AACb,WAAS;AACT,WAAS;AACT,mBAAiB;AAClB;AAEA,CAAC,kBAAkB,CAAC;AACnB,WAAS;AACT,eAAa;AACb,iBAAe;AACf,iBAAe;AAChB;AAEA,CAPC,kBAOkB,CAPC,oBAOoB;AACvC,oBAAkB,IAAI;AACvB;AAEA,CAXC,kBAWkB,CAXC,qBAWqB,EAAE,CAzD1C,oBAyD+D,CAvB1C;AAwBrB,gBAAc;AACf;AAEA,CAfC,kBAekB,CAfC,qBAeqB,EAAE,CAAC;AAC3C,aAAW;AACZ;AAGA,CAlEC,oBAkEoB,CAhCC,eAgCe,KAAK,CAAC,QAAQ;AAClD,cAAY;AACb;;;ACpEA,CAAC;AACA,YAAU;AACV,WAAS;AACT,WAAS;AACT,cAAY;AACZ,iBAAe;AAGf,aAAW;AACZ;AAEA,CAXC,gBAWgB,EAAE,CAAC,UAAU,EAAE,CAAC;AACjC,CAZC,gBAYgB,EAAE,CADC,UACU,EAAE,CAAC;AAGhC,WAAS,IAAI;AACd;AAEA,CAlBC,gBAkBgB,EAAE,CAPC;AAQnB,YAAU;AACV,SAAO;AACR;AAEA,CAvBC,gBAuBgB,EAAE,CAZC,UAYU,EAAE,CAZC;AAahC,WAAS;AACT,cAAY;AACZ,SAAO;AACP,UAAQ;AACR,eAAa;AACb,UAAQ;AACR,eAAa;AACb,aAAW;AACX,UAAQ;AACR,SAAO;AACR;AAEA,CApCC,gBAoCgB,EAAE,CAzBC,UAyBU,EAAE;AAC/B,iBAAe;AAChB;AAEA,CAxCC,gBAwCgB,EAAE,CA7BC,UA6BU,EAAE,QAAQ,CA7BP;AA8BhC,WAAS;AACT,mBAAiB;AACjB,WAAS;AACV;AAEA,CA9CC,gBA8CgB,EAAE,CAnCC,UAmCU,EAAE,QAAQ,CAnCP,KAmCa;AAC7C,WAAS;AACV;AAEA,CAlDC,gBAkDgB,EAAE,CAvCC,UAuCU,EAAE,QAAQ,CAvCP,KAuCa,CAAC;AAC9C,eAAa;AACd;AAEA,CAtDC,gBAsDgB,EAAE,CA3CC,UA2CU,EAAE,CA1CC;AA2ChC,YAAU;AACV,WAAS;AACT,SAAO;AACP,OAAK;AACL,QAAM;AACN,cAAY;AACZ,eAAa;AACb,cAAY;AACZ,aAAW;AACZ;AAIA,CAAC;AACA,cAAY;AACb;AAEA,CAJC,0BAI0B,CAAC;AAC3B,WAAS;AACT,YAAU;AACV,cAAY;AACZ,SAAO;AACP,cAAY;AACZ,WAAS;AACT,aAAW;AACX,eAAa;AACb,cAAY;AACZ,aAAW;AACZ;AAGA,CAtFC,gBAsFgB,CAAC;AACjB,YAAU;AACV,SAAO;AACP,OAAK;AACN;AAEA,CA5FC,gBA4FgB,CANC,kBAMkB,CAAC;AACpC,eAAa;AACd;AAEA,CAhGC,gBAgGgB,CAVC,kBAUkB,CAJC,YAIY,CAAC;AACjD,qBAAmB;AACnB,SAAO;AACP,UAAQ;AACT;;;ACnGA,CAAC;AACA,YAAU;AACX;AAEA,CAJC,iBAIiB,CAAC;AAClB,aAAW;AACX,SAAO;AACR;AAEA,CATC,iBASiB,EAAE,CAAC;AACpB,YAAU;AACV,OAAK;AACL,SAAO;AACR;AAEA,CAAC,GAAG,CAfH,gBAeoB,CAAC;AACrB,oBAAkB;AACnB;AAGA,CAAC,QAAQ,CApBR,gBAoByB,CALJ;AAMrB,oBAAkB;AACnB;AAGA,CAzBC,gBAyBgB,CAAC,YAAY,CAhBT;AAiBrB,CAAC,SAAS,CA1BT,gBA0B0B,CADT,YACsB,CAjBnB;AAkBpB,aAAW,6BAA6B,MAAM,OAAO;AACtD;AAEA,CA9BC,gBA8BgB,CAAC,YAAY,CArBT;AAsBrB,CALC,SAKS,CA/BT,gBA+B0B,CADT,YACsB,CAtBnB;AAuBpB,aAAW,6BAA6B,MAAM,OAAO;AACtD;AAEA,CAAC,SAAS,CAnCT,gBAmC0B,CAVT,YAUsB,CA1BnB;AA2BrB,CAhBC,QAgBS,CApCT,gBAoC0B,CAXT,YAWsB,CA3BnB;AA4BpB,aAAW,kCAAkC,MAAM,OAAO;AAC3D;AAEA,CALC,SAKS,CAxCT,gBAwC0B,CAVT,YAUsB,CA/BnB;AAgCrB,CArBC,QAqBS,CAzCT,gBAyC0B,CAXT,YAWsB,CAhCnB;AAiCpB,aAAW,kCAAkC,MAAM,OAAO;AAC3D;AAEA,WAlBY;AAmBX;AAAK,gBAAY,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE;AAAM;AACzC;AAAO,gBAAY;AAAa;AACjC;AACA,WAjBY;AAkBX;AAAK,gBAAY,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE;AAAM;AAEzC;AAAM,gBAAY;AAAa;AAChC;AAEA,WAlBY;AAmBX;AAAK,gBAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAAO;AAC5C;AAAO,gBAAY;AAAa;AACjC;AACA,WAjBY;AAkBX;AAAK,gBAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAAO;AAE5C;AAAM,gBAAY;AAAa;AAChC;;;AChEA,CAAC;AACA,WAAS;AACT,UAAQ;AACR,eAAa;AACb,YAAU;AACX;AAEA,CAPC,aAOa,CAAC;AACd,UAAQ;AACT;AACA,CAAC;AACA,UAAQ;AACR,YAAU;AACV,OAAK;AACL,QAAM;AACN,kBAAgB;AACjB;AAEA,CAAC,YAAY,CARZ;AASA,QAAM;AACP;AAEA,CAZC,iBAYiB,EAAE,CAAC;AACpB,WAAS;AACT,cAAY;AACZ,UAAQ;AACR,eAAa,IAAI,MAAM;AACxB;AAEA,CAAC,gBAAgB,KAAK,CAAC,eAAe,CAnBrC,iBAmBuD,EAAE,CAPrC;AAQpB,cAAY,aAAa,KAAK;AAC/B;AAEA,CAAC;AACD,CAAC;AACA,UAAQ;AACT;AAEA,CALC;AAMA,aAAW;AACX,cAAY;AACZ,iBAAe;AACf,eAAa;AACb,SAAO;AACP,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,aAAW,WAAW;AACvB;AAEA,CAhBC;AAiBA,QAAM;AACN,YAAU;AACX;AAEA,CAtBC,iBAsBiB;AACjB,iBAAe;AAChB;AAEA,CA1BC,iBA0BiB,CAAC,SAAS;AAC3B,aAAW,OAAO;AACnB;AAEA,CA9BC,iBA8BiB,CAAC,yBAAyB;AAE3C,aAAW,aAAa,MAAM,MAAM,IAAI;AACzC;AAEA,CAAC;AACA,YAAU;AACV,OAAK;AACL,SAAO;AACP,WAAS;AACT,WAAS;AACT,aAAW;AACX,WAAS;AACT,UAAQ,EAAE,KAAK,EAAE;AACjB,UAAQ,IAAI,MAAM,IAAI;AACtB,6BAA2B;AAC3B,8BAA4B;AAC7B;AAEA,CArDC,gBAqDgB,KAAK,CArDC,eAqDe,CAdrC;AAeA,cAAY,IAAI;AACjB;AAEA,CAlBC,uBAkBuB,CA/ET;AAgFd,OAAK;AACN;AAEA,CAAC;AACA,QAAM;AACP;AAEA,CAJC,8BAI8B,CAAC;AAC/B,UAAQ;AACT;AAEA,CARC,8BAQ8B,CAJC,gBAIgB,EAAE,CAAC,UAAU,EAAE,CAAC;AAChE,CATC,8BAS8B,CALC,gBAKgB,EAAE,CADC,UACU,EAAE,CAAC;AAC/D,WAAS,IAAI;AACd;AAEA,CAbC,8BAa8B,CAAC,iBAAiB,EAAE,CAAC;AACnD,OAAK;AACN;AAEA,CAAC;AACA,eAAa;AACd;AAEA,CAJC,kCAIkC,CAAC,kBAAkB,CAAC;AACtD,WAAS;AACV;AAEA,CAAC,YAAY,CAAC,0BAA0B,CAAC;AACxC,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR,WAAS;AAGT,oBAAkB,IAAI;AACvB;AAEA,CAZC,YAYY,CAZC,0BAY0B,CAZC,6BAY6B,CAAC,sBAAsB,CAAC;AAC7F,YAAU;AACV,SAAO;AACP,WAAS;AACT,YAAU;AAGV,oBAAkB,IAAI;AACvB;AAEA,CAtBC,YAsBY,CAtBC,0BAsB0B,CAtBC,6BAsB6B,CAVC,sBAUsB;AAC5F,oBAAkB,IAAI;AACtB,UAAQ;AACT;AAEA,CA3BC,YA2BY,CA3BC,0BA2B0B,CA3BC,4BA2B4B,CAAC;AACtE,CA5BC,YA4BY,CA5BC,0BA4B0B,CA5BC,4BA4B4B,CADC,MACM,CAAC;AAC5E,WAAS;AACV;AAEA,CAhCC,YAgCY,CAhCC,0BAgC0B,CAhCC,6BAgC6B,CAJO;AAK5E,YAAU;AACV,UAAQ;AACR,QAAM;AACN,UAAQ;AACR,SAAO;AACR;AAEA,CAxCC,YAwCY,CAxCC,0BAwC0B,CAxCC,4BAwC4B,CAAC,aAAa;AAClF,WAAS;AACV;", "names": []}