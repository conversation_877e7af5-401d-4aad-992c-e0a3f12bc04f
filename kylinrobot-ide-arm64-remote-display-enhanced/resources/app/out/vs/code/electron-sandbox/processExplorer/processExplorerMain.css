/*!--------------------------------------------------------
 * Copyright (C) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------*/
/* out-build/vs/code/electron-sandbox/processExplorer/media/processExplorer.css */
html {
  font-size: 13px;
}
.mac {
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;
}
.mac:lang(zh-<PERSON>) {
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    "PingFang SC",
    "Hiragino Sans GB",
    sans-serif;
}
.mac:lang(zh-Hant) {
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    "PingFang TC",
    sans-serif;
}
.mac:lang(ja) {
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    "Hiragino Kaku Gothic Pro",
    sans-serif;
}
.mac:lang(ko) {
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    "Apple SD Gothic Neo",
    "Nanum Gothic",
    "AppleGothic",
    sans-serif;
}
.windows {
  font-family:
    "Segoe WPC",
    "Segoe UI",
    sans-serif;
}
.windows:lang(zh-Hans) {
  font-family:
    "Segoe WPC",
    "Segoe UI",
    "Microsoft YaHei",
    sans-serif;
}
.windows:lang(zh-Hant) {
  font-family:
    "Segoe WPC",
    "Segoe UI",
    "Microsoft Jhenghei",
    sans-serif;
}
.windows:lang(ja) {
  font-family:
    "Segoe WPC",
    "Segoe UI",
    "Yu Gothic UI",
    "Meiryo UI",
    sans-serif;
}
.windows:lang(ko) {
  font-family:
    "Segoe WPC",
    "Segoe UI",
    "Malgun Gothic",
    "Dotom",
    sans-serif;
}
.linux {
  font-family:
    system-ui,
    "Ubuntu",
    "Droid Sans",
    sans-serif;
}
.linux:lang(zh-Hans) {
  font-family:
    system-ui,
    "Ubuntu",
    "Droid Sans",
    "Source Han Sans SC",
    "Source Han Sans CN",
    "Source Han Sans",
    sans-serif;
}
.linux:lang(zh-Hant) {
  font-family:
    system-ui,
    "Ubuntu",
    "Droid Sans",
    "Source Han Sans TC",
    "Source Han Sans TW",
    "Source Han Sans",
    sans-serif;
}
.linux:lang(ja) {
  font-family:
    system-ui,
    "Ubuntu",
    "Droid Sans",
    "Source Han Sans J",
    "Source Han Sans JP",
    "Source Han Sans",
    sans-serif;
}
.linux:lang(ko) {
  font-family:
    system-ui,
    "Ubuntu",
    "Droid Sans",
    "Source Han Sans K",
    "Source Han Sans JR",
    "Source Han Sans",
    "UnDotum",
    "FBaekmuk Gulim",
    sans-serif;
}
body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  user-select: none;
  color: #cccccc;
}
.cpu {
  width: 60px;
}
.pid {
  width: 50px;
}
.memory {
  width: 90px;
}
.monaco-list:focus {
  outline: 0;
}
.monaco-list-row:first-of-type {
  border-bottom: 1px solid;
}
.row {
  display: flex;
}
.centered {
  text-align: center;
}
.nameLabel {
  text-align: left;
  width: calc(100% - 185px);
  overflow: hidden;
  text-overflow: ellipsis;
}
.data {
  white-space: pre;
  padding-left: .5rem;
  font-weight: normal;
  text-align: left;
  height: 22px;
}
.error {
  padding-left: 20px;
  white-space: nowrap;
}

/* out-build/vs/base/browser/ui/codicons/codicon/codicon.css */
@font-face {
  font-family: "codicon";
  font-display: block;
  src: url("../../../../media/codicon.ttf?5d4d76ab2ce5108968ad644d591a16a6") format("truetype");
}
.codicon[class*=codicon-] {
  font: normal normal normal 16px/1 codicon;
  display: inline-block;
  text-decoration: none;
  text-rendering: auto;
  text-align: center;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  user-select: none;
  -webkit-user-select: none;
}

/* out-build/vs/base/browser/ui/codicons/codicon/codicon-modifiers.css */
.codicon-wrench-subaction {
  opacity: 0.5;
}
@keyframes codicon-spin {
  100% {
    transform: rotate(360deg);
  }
}
.codicon-sync.codicon-modifier-spin,
.codicon-loading.codicon-modifier-spin,
.codicon-gear.codicon-modifier-spin,
.codicon-notebook-state-executing.codicon-modifier-spin {
  animation: codicon-spin 1.5s steps(30) infinite;
}
.codicon-modifier-disabled {
  opacity: 0.4;
}
.codicon-loading,
.codicon-tree-item-loading::before {
  animation-duration: 1s !important;
  animation-timing-function: cubic-bezier(0.53, 0.21, 0.29, 0.67) !important;
}

/* out-build/vs/base/browser/ui/aria/aria.css */
.monaco-aria-container {
  position: absolute;
  left: -999em;
}

/* out-build/vs/base/browser/ui/list/list.css */
.monaco-list {
  position: relative;
  height: 100%;
  width: 100%;
  white-space: nowrap;
}
.monaco-list.mouse-support {
  user-select: none;
  -webkit-user-select: none;
}
.monaco-list > .monaco-scrollable-element {
  height: 100%;
}
.monaco-list-rows {
  position: relative;
  width: 100%;
  height: 100%;
}
.monaco-list.horizontal-scrolling .monaco-list-rows {
  width: auto;
  min-width: 100%;
}
.monaco-list-row {
  position: absolute;
  box-sizing: border-box;
  overflow: hidden;
  width: 100%;
}
.monaco-list.mouse-support .monaco-list-row {
  cursor: pointer;
  touch-action: none;
}
.monaco-list .monaco-scrollable-element > .scrollbar.vertical,
.monaco-pane-view > .monaco-split-view2.vertical > .monaco-scrollable-element > .scrollbar.vertical {
  z-index: 14;
}
.monaco-list-row.scrolling {
  display: none !important;
}
.monaco-list.element-focused,
.monaco-list.selection-single,
.monaco-list.selection-multiple {
  outline: 0 !important;
}
.monaco-drag-image {
  display: inline-block;
  padding: 1px 7px;
  border-radius: 10px;
  font-size: 12px;
  position: absolute;
  z-index: 1000;
}
.monaco-list-type-filter-message {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  padding: 40px 1em 1em 1em;
  text-align: center;
  white-space: normal;
  opacity: 0.7;
  pointer-events: none;
}
.monaco-list-type-filter-message:empty {
  display: none;
}

/* out-build/vs/base/browser/ui/scrollbar/media/scrollbars.css */
.monaco-scrollable-element > .scrollbar > .scra {
  cursor: pointer;
  font-size: 11px !important;
}
.monaco-scrollable-element > .visible {
  opacity: 1;
  background: rgba(0, 0, 0, 0);
  transition: opacity 100ms linear;
  z-index: 11;
}
.monaco-scrollable-element > .invisible {
  opacity: 0;
  pointer-events: none;
}
.monaco-scrollable-element > .invisible.fade {
  transition: opacity 800ms linear;
}
.monaco-scrollable-element > .shadow {
  position: absolute;
  display: none;
}
.monaco-scrollable-element > .shadow.top {
  display: block;
  top: 0;
  left: 3px;
  height: 3px;
  width: 100%;
  box-shadow: var(--vscode-scrollbar-shadow) 0 6px 6px -6px inset;
}
.monaco-scrollable-element > .shadow.left {
  display: block;
  top: 3px;
  left: 0;
  height: 100%;
  width: 3px;
  box-shadow: var(--vscode-scrollbar-shadow) 6px 0 6px -6px inset;
}
.monaco-scrollable-element > .shadow.top-left-corner {
  display: block;
  top: 0;
  left: 0;
  height: 3px;
  width: 3px;
}
.monaco-scrollable-element > .shadow.top.left {
  box-shadow: var(--vscode-scrollbar-shadow) 6px 0 6px -6px inset;
}
.monaco-scrollable-element > .scrollbar > .slider {
  background: var(--vscode-scrollbarSlider-background);
}
.monaco-scrollable-element > .scrollbar > .slider:hover {
  background: var(--vscode-scrollbarSlider-hoverBackground);
}
.monaco-scrollable-element > .scrollbar > .slider.active {
  background: var(--vscode-scrollbarSlider-activeBackground);
}

/* out-build/vs/base/browser/ui/selectBox/selectBoxCustom.css */
.monaco-select-box-dropdown-padding {
  --dropdown-padding-top: 1px;
  --dropdown-padding-bottom: 1px;
}
.hc-black .monaco-select-box-dropdown-padding,
.hc-light .monaco-select-box-dropdown-padding {
  --dropdown-padding-top: 3px;
  --dropdown-padding-bottom: 4px;
}
.monaco-select-box-dropdown-container {
  display: none;
  box-sizing: border-box;
}
.monaco-select-box-dropdown-container > .select-box-details-pane > .select-box-description-markdown * {
  margin: 0;
}
.monaco-select-box-dropdown-container > .select-box-details-pane > .select-box-description-markdown a:focus {
  outline: 1px solid -webkit-focus-ring-color;
  outline-offset: -1px;
}
.monaco-select-box-dropdown-container > .select-box-details-pane > .select-box-description-markdown code {
  line-height: 15px;
  font-family: var(--monaco-monospace-font);
}
.monaco-select-box-dropdown-container.visible {
  display: flex;
  flex-direction: column;
  text-align: left;
  width: 1px;
  overflow: hidden;
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
}
.monaco-select-box-dropdown-container > .select-box-dropdown-list-container {
  flex: 0 0 auto;
  align-self: flex-start;
  padding-top: var(--dropdown-padding-top);
  padding-bottom: var(--dropdown-padding-bottom);
  padding-left: 1px;
  padding-right: 1px;
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}
.monaco-select-box-dropdown-container > .select-box-details-pane {
  padding: 5px;
}
.hc-black .monaco-select-box-dropdown-container > .select-box-dropdown-list-container {
  padding-top: var(--dropdown-padding-top);
  padding-bottom: var(--dropdown-padding-bottom);
}
.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row {
  cursor: pointer;
}
.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row > .option-text {
  text-overflow: ellipsis;
  overflow: hidden;
  padding-left: 3.5px;
  white-space: nowrap;
  float: left;
}
.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row > .option-detail {
  text-overflow: ellipsis;
  overflow: hidden;
  padding-left: 3.5px;
  white-space: nowrap;
  float: left;
  opacity: 0.7;
}
.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row > .option-decorator-right {
  text-overflow: ellipsis;
  overflow: hidden;
  padding-right: 10px;
  white-space: nowrap;
  float: right;
}
.monaco-select-box-dropdown-container > .select-box-dropdown-list-container .monaco-list .monaco-list-row > .visually-hidden {
  position: absolute;
  left: -10000px;
  top: auto;
  width: 1px;
  height: 1px;
  overflow: hidden;
}
.monaco-select-box-dropdown-container > .select-box-dropdown-container-width-control {
  flex: 1 1 auto;
  align-self: flex-start;
  opacity: 0;
}
.monaco-select-box-dropdown-container > .select-box-dropdown-container-width-control > .width-control-div {
  overflow: hidden;
  max-height: 0px;
}
.monaco-select-box-dropdown-container > .select-box-dropdown-container-width-control > .width-control-div > .option-text-width-control {
  padding-left: 4px;
  padding-right: 8px;
  white-space: nowrap;
}

/* out-build/vs/base/browser/ui/selectBox/selectBox.css */
.monaco-select-box {
  width: 100%;
  cursor: pointer;
  border-radius: 2px;
}
.monaco-select-box-dropdown-container {
  font-size: 13px;
  font-weight: normal;
  text-transform: none;
}
.monaco-action-bar .action-item.select-container {
  cursor: default;
}
.monaco-action-bar .action-item .monaco-select-box {
  cursor: pointer;
  min-width: 100px;
  min-height: 18px;
  padding: 2px 23px 2px 8px;
}
.mac .monaco-action-bar .action-item .monaco-select-box {
  font-size: 11px;
  border-radius: 5px;
}

/* out-build/vs/base/browser/ui/actionbar/actionbar.css */
.monaco-action-bar {
  white-space: nowrap;
  height: 100%;
}
.monaco-action-bar .actions-container {
  display: flex;
  margin: 0 auto;
  padding: 0;
  height: 100%;
  width: 100%;
  align-items: center;
}
.monaco-action-bar.vertical .actions-container {
  display: inline-block;
}
.monaco-action-bar .action-item {
  display: block;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
}
.monaco-action-bar .action-item.disabled {
  cursor: default;
}
.monaco-action-bar .action-item .icon,
.monaco-action-bar .action-item .codicon {
  display: block;
}
.monaco-action-bar .action-item .codicon {
  display: flex;
  align-items: center;
  width: 16px;
  height: 16px;
}
.monaco-action-bar .action-label {
  display: flex;
  font-size: 11px;
  padding: 3px;
  border-radius: 5px;
}
.monaco-action-bar .action-item.disabled .action-label:not(.icon),
.monaco-action-bar .action-item.disabled .action-label:not(.icon)::before,
.monaco-action-bar .action-item.disabled .action-label:not(.icon):hover {
  color: var(--vscode-disabledForeground);
}
.monaco-action-bar .action-item.disabled .action-label.icon,
.monaco-action-bar .action-item.disabled .action-label.icon::before,
.monaco-action-bar .action-item.disabled .action-label.icon:hover {
  opacity: 0.6;
}
.monaco-action-bar.vertical {
  text-align: left;
}
.monaco-action-bar.vertical .action-item {
  display: block;
}
.monaco-action-bar.vertical .action-label.separator {
  display: block;
  border-bottom: 1px solid #bbb;
  padding-top: 1px;
  margin-left: .8em;
  margin-right: .8em;
}
.monaco-action-bar .action-item .action-label.separator {
  width: 1px;
  height: 16px;
  margin: 5px 4px !important;
  cursor: default;
  min-width: 1px;
  padding: 0;
  background-color: #bbb;
}
.secondary-actions .monaco-action-bar .action-label {
  margin-left: 6px;
}
.monaco-action-bar .action-item.select-container {
  overflow: hidden;
  flex: 1;
  max-width: 170px;
  min-width: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}
.monaco-action-bar .action-item.action-dropdown-item {
  display: flex;
}
.monaco-action-bar .action-item.action-dropdown-item > .action-dropdown-item-separator {
  display: flex;
  align-items: center;
  cursor: default;
}
.monaco-action-bar .action-item.action-dropdown-item > .action-dropdown-item-separator > div {
  width: 1px;
}

/* out-build/vs/base/browser/ui/toggle/toggle.css */
.monaco-custom-toggle {
  margin-left: 2px;
  float: left;
  cursor: pointer;
  overflow: hidden;
  width: 20px;
  height: 20px;
  border-radius: 3px;
  border: 1px solid transparent;
  padding: 1px;
  box-sizing: border-box;
  user-select: none;
  -webkit-user-select: none;
}
.monaco-custom-toggle:hover {
  background-color: var(--vscode-inputOption-hoverBackground);
}
.hc-black .monaco-custom-toggle:hover,
.hc-light .monaco-custom-toggle:hover {
  border: 1px dashed var(--vscode-focusBorder);
}
.hc-black .monaco-custom-toggle,
.hc-light .monaco-custom-toggle {
  background: none;
}
.hc-black .monaco-custom-toggle:hover,
.hc-light .monaco-custom-toggle:hover {
  background: none;
}
.monaco-custom-toggle.monaco-checkbox {
  height: 18px;
  width: 18px;
  border: 1px solid transparent;
  border-radius: 3px;
  margin-right: 9px;
  margin-left: 0px;
  padding: 0px;
  opacity: 1;
  background-size: 16px !important;
}
.monaco-action-bar .checkbox-action-item {
  display: flex;
  align-items: center;
  border-radius: 2px;
  padding-right: 2px;
}
.monaco-action-bar .checkbox-action-item:hover {
  background-color: var(--vscode-toolbar-hoverBackground);
}
.monaco-action-bar .checkbox-action-item > .monaco-custom-toggle.monaco-checkbox {
  margin-right: 4px;
}
.monaco-action-bar .checkbox-action-item > .checkbox-label {
  font-size: 12px;
}
.monaco-custom-toggle.monaco-checkbox:not(.checked)::before {
  visibility: hidden;
}

/* out-build/vs/base/browser/ui/inputbox/inputBox.css */
.monaco-inputbox {
  position: relative;
  display: block;
  padding: 0;
  box-sizing: border-box;
  border-radius: 2px;
  font-size: inherit;
}
.monaco-inputbox > .ibwrapper > .input,
.monaco-inputbox > .ibwrapper > .mirror {
  padding: 4px 6px;
}
.monaco-inputbox > .ibwrapper {
  position: relative;
  width: 100%;
}
.monaco-inputbox > .ibwrapper > .input {
  display: inline-block;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  line-height: inherit;
  border: none;
  font-family: inherit;
  font-size: inherit;
  resize: none;
  color: inherit;
}
.monaco-inputbox > .ibwrapper > input {
  text-overflow: ellipsis;
}
.monaco-inputbox > .ibwrapper > textarea.input {
  display: block;
  scrollbar-width: none;
  outline: none;
}
.monaco-inputbox > .ibwrapper > textarea.input::-webkit-scrollbar {
  display: none;
}
.monaco-inputbox > .ibwrapper > textarea.input.empty {
  white-space: nowrap;
}
.monaco-inputbox > .ibwrapper > .mirror {
  position: absolute;
  display: inline-block;
  width: 100%;
  top: 0;
  left: 0;
  box-sizing: border-box;
  white-space: pre-wrap;
  visibility: hidden;
  word-wrap: break-word;
}
.monaco-inputbox-container {
  text-align: right;
}
.monaco-inputbox-container .monaco-inputbox-message {
  display: inline-block;
  overflow: hidden;
  text-align: left;
  width: 100%;
  box-sizing: border-box;
  padding: 0.4em;
  font-size: 12px;
  line-height: 17px;
  margin-top: -1px;
  word-wrap: break-word;
}
.monaco-inputbox .monaco-action-bar {
  position: absolute;
  right: 2px;
  top: 4px;
}
.monaco-inputbox .monaco-action-bar .action-item {
  margin-left: 2px;
}
.monaco-inputbox .monaco-action-bar .action-item .codicon {
  background-repeat: no-repeat;
  width: 16px;
  height: 16px;
}

/* out-build/vs/base/browser/ui/findinput/findInput.css */
.monaco-findInput {
  position: relative;
}
.monaco-findInput .monaco-inputbox {
  font-size: 13px;
  width: 100%;
}
.monaco-findInput > .controls {
  position: absolute;
  top: 3px;
  right: 2px;
}
.vs .monaco-findInput.disabled {
  background-color: #E1E1E1;
}
.vs-dark .monaco-findInput.disabled {
  background-color: #333;
}
.monaco-findInput.highlight-0 .controls,
.hc-light .monaco-findInput.highlight-0 .controls {
  animation: monaco-findInput-highlight-0 100ms linear 0s;
}
.monaco-findInput.highlight-1 .controls,
.hc-light .monaco-findInput.highlight-1 .controls {
  animation: monaco-findInput-highlight-1 100ms linear 0s;
}
.hc-black .monaco-findInput.highlight-0 .controls,
.vs-dark .monaco-findInput.highlight-0 .controls {
  animation: monaco-findInput-highlight-dark-0 100ms linear 0s;
}
.hc-black .monaco-findInput.highlight-1 .controls,
.vs-dark .monaco-findInput.highlight-1 .controls {
  animation: monaco-findInput-highlight-dark-1 100ms linear 0s;
}
@keyframes monaco-findInput-highlight-0 {
  0% {
    background: rgba(253, 255, 0, 0.8);
  }
  100% {
    background: transparent;
  }
}
@keyframes monaco-findInput-highlight-1 {
  0% {
    background: rgba(253, 255, 0, 0.8);
  }
  99% {
    background: transparent;
  }
}
@keyframes monaco-findInput-highlight-dark-0 {
  0% {
    background: rgba(255, 255, 255, 0.44);
  }
  100% {
    background: transparent;
  }
}
@keyframes monaco-findInput-highlight-dark-1 {
  0% {
    background: rgba(255, 255, 255, 0.44);
  }
  99% {
    background: transparent;
  }
}

/* out-build/vs/base/browser/ui/tree/media/tree.css */
.monaco-tl-row {
  display: flex;
  height: 100%;
  align-items: center;
  position: relative;
}
.monaco-tl-row.disabled {
  cursor: default;
}
.monaco-tl-indent {
  height: 100%;
  position: absolute;
  top: 0;
  left: 16px;
  pointer-events: none;
}
.hide-arrows .monaco-tl-indent {
  left: 12px;
}
.monaco-tl-indent > .indent-guide {
  display: inline-block;
  box-sizing: border-box;
  height: 100%;
  border-left: 1px solid transparent;
}
.monaco-workbench:not(.reduce-motion) .monaco-tl-indent > .indent-guide {
  transition: border-color 0.1s linear;
}
.monaco-tl-twistie,
.monaco-tl-contents {
  height: 100%;
}
.monaco-tl-twistie {
  font-size: 10px;
  text-align: right;
  padding-right: 6px;
  flex-shrink: 0;
  width: 16px;
  display: flex !important;
  align-items: center;
  justify-content: center;
  transform: translateX(3px);
}
.monaco-tl-contents {
  flex: 1;
  overflow: hidden;
}
.monaco-tl-twistie::before {
  border-radius: 20px;
}
.monaco-tl-twistie.collapsed::before {
  transform: rotate(-90deg);
}
.monaco-tl-twistie.codicon-tree-item-loading::before {
  animation: codicon-spin 1.25s steps(30) infinite;
}
.monaco-tree-type-filter {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  padding: 3px;
  max-width: 200px;
  z-index: 100;
  margin: 0 10px 0 6px;
  border: 1px solid var(--vscode-widget-border);
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}
.monaco-workbench:not(.reduce-motion) .monaco-tree-type-filter {
  transition: top 0.3s;
}
.monaco-tree-type-filter.disabled {
  top: -40px !important;
}
.monaco-tree-type-filter-input {
  flex: 1;
}
.monaco-tree-type-filter-input .monaco-inputbox {
  height: 23px;
}
.monaco-tree-type-filter-input .monaco-inputbox > .ibwrapper > .input,
.monaco-tree-type-filter-input .monaco-inputbox > .ibwrapper > .mirror {
  padding: 2px 4px;
}
.monaco-tree-type-filter-input .monaco-findInput > .controls {
  top: 2px;
}
.monaco-tree-type-filter-actionbar {
  margin-left: 4px;
}
.monaco-tree-type-filter-actionbar .monaco-action-bar .action-label {
  padding: 2px;
}
.monaco-list .monaco-scrollable-element .monaco-tree-sticky-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
  z-index: 13;
  background-color: var(--vscode-sideBar-background);
}
.monaco-list .monaco-scrollable-element .monaco-tree-sticky-container .monaco-tree-sticky-row.monaco-list-row {
  position: absolute;
  width: 100%;
  opacity: 1 !important;
  overflow: hidden;
  background-color: var(--vscode-sideBar-background);
}
.monaco-list .monaco-scrollable-element .monaco-tree-sticky-container .monaco-tree-sticky-row:hover {
  background-color: var(--vscode-list-hoverBackground) !important;
  cursor: pointer;
}
.monaco-list .monaco-scrollable-element .monaco-tree-sticky-container.empty,
.monaco-list .monaco-scrollable-element .monaco-tree-sticky-container.empty .monaco-tree-sticky-container-shadow {
  display: none;
}
.monaco-list .monaco-scrollable-element .monaco-tree-sticky-container .monaco-tree-sticky-container-shadow {
  position: absolute;
  bottom: -3px;
  left: 0px;
  height: 0px;
  width: 100%;
}
.monaco-list .monaco-scrollable-element .monaco-tree-sticky-container[tabindex="0"]:focus {
  outline: none;
}
