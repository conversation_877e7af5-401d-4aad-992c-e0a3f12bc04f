#!/bin/bash
# 远程显示功能增强版 - 基于官方对齐版本，增加远程显示功能支持
# 目标：在保持官方VS Code包结构的基础上，确保远程显示功能完整可用

set -e

# 默认参数
ARCH="arm64"
DEB_ARCH="arm64"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    --arch)
      ARCH="$2"
      shift 2
      ;;
    *)
      echo "未知选项: $1"
      echo "用法: $0 [--arch <x64|arm64|armhf>]"
      exit 1
      ;;
  esac
done

# 设置对应的Debian架构
case "$ARCH" in
  "x64") DEB_ARCH="amd64" ;;
  "arm64") DEB_ARCH="arm64" ;;
  "armhf") DEB_ARCH="armhf" ;;
  *)
    echo "错误: 不支持的架构 '$ARCH'"
    exit 1
    ;;
esac

# 获取版本信息
PACKAGE_VERSION=$(node -pe "require('./package.json').version" 2>/dev/null || echo "1.102.0")
BUILD_TIMESTAMP=$(date +%s)

echo "=== 远程显示功能增强版 KylinRobot $ARCH DEB 包构建 ==="
echo "架构: $ARCH ($DEB_ARCH)"
echo "版本: $PACKAGE_VERSION"
echo "时间戳: $BUILD_TIMESTAMP"
echo "特性: 官方对齐 + 远程显示功能完整支持"
echo ""

# 1. 环境检查（增强版）
echo "=== 增强环境检查 ==="
if [ ! -f "package.json" ] || [ ! -f "product.json" ]; then
  echo "错误: 当前目录不是有效的 VSCode 项目根目录"
  exit 1
fi

if [ ! -d "node_modules" ]; then
  echo "错误: node_modules 目录不存在"
  exit 1
fi

if [ ! -d "out" ]; then
  echo "错误: out 目录不存在，请先运行编译"
  exit 1
fi

# 远程显示功能专项检查
echo "🖥️  检查远程显示功能..."
REMOTE_DISPLAY_EXT="extensions/remote-display"
if [ ! -d "$REMOTE_DISPLAY_EXT" ]; then
  echo "❌ 远程显示扩展目录不存在: $REMOTE_DISPLAY_EXT"
  exit 1
fi

if [ ! -f "$REMOTE_DISPLAY_EXT/package.json" ]; then
  echo "❌ 远程显示扩展配置不存在: $REMOTE_DISPLAY_EXT/package.json"
  exit 1
fi

if [ ! -d "$REMOTE_DISPLAY_EXT/out" ]; then
  echo "❌ 远程显示扩展未编译，请先运行: cd $REMOTE_DISPLAY_EXT && npm run compile"
  exit 1
fi

# 检查关键资源文件（增强版）
REMOTE_DISPLAY_RESOURCES=(
  "$REMOTE_DISPLAY_EXT/resources/noVNC-client/core/rfb.js"
  "$REMOTE_DISPLAY_EXT/resources/noVNC-client/core"
  "$REMOTE_DISPLAY_EXT/out/extension.js"
  "$REMOTE_DISPLAY_EXT/out/websocketVNCBridge.js"
  "$REMOTE_DISPLAY_EXT/out/noVNCEmbeddedPanel.js"
  "$REMOTE_DISPLAY_EXT/package.json"
)

for resource in "${REMOTE_DISPLAY_RESOURCES[@]}"; do
  if [ -e "$resource" ]; then
    if [ -f "$resource" ]; then
      size=$(stat -c%s "$resource" 2>/dev/null || echo "unknown")
      echo "✅ 关键资源文件: $resource (${size} bytes)"
    else
      echo "✅ 关键资源目录: $resource"
    fi
  else
    echo "❌ 缺失关键资源: $resource"
    exit 1
  fi
done

# 特别检查noVNC核心文件的内容完整性
NOVNC_CORE_FILE="$REMOTE_DISPLAY_EXT/resources/noVNC-client/core/rfb.js"
if [ -f "$NOVNC_CORE_FILE" ]; then
  # 检查文件是否包含RFB类定义
  if grep -q "class RFB\|function RFB\|RFB.*=" "$NOVNC_CORE_FILE"; then
    echo "✅ noVNC核心文件包含RFB定义"
  else
    echo "⚠️  noVNC核心文件可能不包含RFB定义，请检查文件内容"
  fi

  # 检查文件大小（正常应该>100KB）
  file_size=$(stat -c%s "$NOVNC_CORE_FILE")
  if [ $file_size -gt 100000 ]; then
    echo "✅ noVNC核心文件大小正常: $file_size bytes"
  else
    echo "⚠️  noVNC核心文件可能不完整: $file_size bytes"
  fi
fi

echo "✅ 远程显示功能检查通过"

# 注意：我们直接使用gulp构建的完整输出，无需与官方包对比

echo "✓ 增强环境检查通过"

# 🔥 验证gulp构建输出，确保版本一致性
echo ""
echo "=== 验证gulp构建输出（VSCode-linux-$ARCH） ==="
echo "🎯 检查gulp构建的完整性，确保与开发环境一致"

BUILD_DIR="../VSCode-linux-$ARCH"

# 检查gulp构建输出目录
if [ ! -d "$BUILD_DIR" ]; then
    echo "❌ gulp构建输出目录不存在: $BUILD_DIR"
    echo "请先运行: npm run gulp vscode-linux-$ARCH"
    exit 1
fi

echo "✅ 找到gulp构建输出: $BUILD_DIR"

# 验证关键文件存在
REQUIRED_BUILD_FILES=(
    "$BUILD_DIR/kylinrobot-ide"
    "$BUILD_DIR/resources/app/package.json"
    "$BUILD_DIR/resources/app/product.json"
    "$BUILD_DIR/resources/app/out/vs/workbench/workbench.desktop.main.js"
    "$BUILD_DIR/resources/app/node_modules"
)

missing_files=0
for file in "${REQUIRED_BUILD_FILES[@]}"; do
    if [ -e "$file" ]; then
        if [ -f "$file" ]; then
            size=$(stat -c%s "$file" 2>/dev/null || echo "unknown")
            echo "✅ 关键文件: $(basename $file) ($size bytes)"
        else
            echo "✅ 关键目录: $(basename $file)"
        fi
    else
        echo "❌ 缺失关键文件: $file"
        missing_files=$((missing_files + 1))
    fi
done

if [ $missing_files -gt 0 ]; then
    echo "❌ 有 $missing_files 个关键文件缺失"
    echo "请重新运行: npm run gulp vscode-linux-$ARCH"
    exit 1
fi

# 验证主workbench文件大小（应该是一个大文件）
main_workbench="$BUILD_DIR/resources/app/out/vs/workbench/workbench.desktop.main.js"
if [ -f "$main_workbench" ]; then
    main_size=$(stat -c%s "$main_workbench")
    if [ $main_size -gt 1000000 ]; then  # 应该大于1MB
        echo "✅ 主workbench文件大小正常: $main_size bytes"
    else
        echo "❌ 主workbench文件可能不完整: $main_size bytes (应该>1MB)"
        exit 1
    fi
fi

echo "✅ gulp构建输出验证通过"

# 验证远程显示扩展在gulp构建输出中的存在性
BUILD_REMOTE_DISPLAY_EXT="$BUILD_DIR/resources/app/extensions/remote-display"
REQUIRED_COMPILED_FILES=(
    "$BUILD_REMOTE_DISPLAY_EXT/out/extension.js"
    "$BUILD_REMOTE_DISPLAY_EXT/out/noVNCEmbeddedPanel.js"
    "$BUILD_REMOTE_DISPLAY_EXT/out/websocketVNCBridge.js"
    "$BUILD_REMOTE_DISPLAY_EXT/resources/noVNC-client/core/rfb.js"
)

missing_compiled=0
for compiled_file in "${REQUIRED_COMPILED_FILES[@]}"; do
    if [ -f "$compiled_file" ]; then
        size=$(stat -c%s "$compiled_file")
        echo "✅ gulp构建扩展验证: $(basename $compiled_file) ($size bytes)"
    else
        echo "❌ gulp构建扩展缺失: $compiled_file"
        missing_compiled=$((missing_compiled + 1))
    fi
done

if [ $missing_compiled -gt 0 ]; then
    echo "❌ 有 $missing_compiled 个扩展文件在gulp构建输出中缺失"
    echo "请检查扩展是否正确编译到gulp构建中"
    exit 1
fi

echo "✅ gulp构建验证完成，包含完整的远程显示扩展"
echo "  📊 构建流程: npm run gulp vscode-linux-$ARCH → 完整应用包含所有扩展"

# 2. 验证gulp构建中的node_modules完整性
echo "=== 验证gulp构建中的node_modules完整性 ==="

BUILD_NODE_MODULES="$BUILD_DIR/resources/app/node_modules"

if [ ! -d "$BUILD_NODE_MODULES" ]; then
    echo "❌ gulp构建中的node_modules不存在: $BUILD_NODE_MODULES"
    exit 1
fi

echo "✅ 找到gulp构建的node_modules: $BUILD_NODE_MODULES"

# 统计gulp构建中的模块数量
gulp_modules_count=$(find "$BUILD_NODE_MODULES" -maxdepth 1 -type d | wc -l)
gulp_files_count=$(find "$BUILD_NODE_MODULES" -type f | wc -l)
gulp_node_files=$(find "$BUILD_NODE_MODULES" -name "*.node" -type f | wc -l)

echo "✅ gulp构建node_modules统计:"
echo "  模块目录数: $gulp_modules_count"
echo "  总文件数: $gulp_files_count"
echo "  .node文件数: $gulp_node_files"

# 验证关键原生模块存在
CRITICAL_NATIVES=(
  "@vscode/sqlite3/build/Release/vscode-sqlite3.node"
  "@vscode/spdlog/build/Release/spdlog.node"
  "native-keymap/build/Release/keymapping.node"
  "node-pty/build/Release/pty.node"
)

missing_natives=0
for native in "${CRITICAL_NATIVES[@]}"; do
  if [ -f "$BUILD_NODE_MODULES/$native" ]; then
    echo "✅ 关键原生模块: $native"
  else
    echo "❌ 缺失关键原生模块: $native"
    missing_natives=$((missing_natives + 1))
  fi
done

if [ $missing_natives -gt 0 ]; then
  echo "❌ 有 $missing_natives 个关键原生模块缺失"
  exit 1
fi

# 验证远程显示相关依赖
REMOTE_DISPLAY_DEPS=(
  "ws/package.json"
  "mqtt/package.json"
  "debug/package.json"
)

missing_remote_deps=0
for dep in "${REMOTE_DISPLAY_DEPS[@]}"; do
  if [ -f "$BUILD_NODE_MODULES/$dep" ]; then
    echo "✅ 远程显示依赖: $dep"
  else
    echo "⚠️  远程显示依赖缺失: $dep"
    missing_remote_deps=$((missing_remote_deps + 1))
  fi
done

if [ $missing_remote_deps -gt 0 ]; then
  echo "⚠️  有 $missing_remote_deps 个远程显示依赖缺失，但不影响基本功能"
fi

echo "✅ gulp构建node_modules验证完成"

# 注意：我们直接使用gulp构建中的完整node_modules，无需创建自定义模块列表

# 3. 创建应用目录结构（增强版）
echo "=== 创建远程显示功能增强应用结构 ==="
APP_DIR="./kylinrobot-ide-$ARCH-remote-display-enhanced"
BUILD_DIR="../VSCode-linux-$ARCH"

rm -rf "$APP_DIR"
mkdir -p "$APP_DIR"

# 基础应用结构构建（与原版相同）
if [ -d "$BUILD_DIR" ]; then
  echo "使用已有的构建目录: $BUILD_DIR"
  cp -r "$BUILD_DIR/"* "$APP_DIR/"

  # 保持gulp构建中的原始node_modules（已经包含所有必要依赖）
  echo "保持gulp构建中的完整node_modules..."
  echo "  gulp构建的node_modules已包含所有运行时依赖，无需替换"

  # 验证gulp构建中的远程显示扩展完整性
  echo "🔨 验证gulp构建中的远程显示扩展..."
  REMOTE_DISPLAY_TARGET="$APP_DIR/resources/app/extensions/remote-display"
  if [ -d "$REMOTE_DISPLAY_TARGET" ]; then
    echo "  ✅ 远程显示扩展已包含在gulp构建中"

    # 验证关键文件
    for resource in "${REMOTE_DISPLAY_RESOURCES[@]}"; do
      target_resource="${resource/extensions\/remote-display/$REMOTE_DISPLAY_TARGET}"
      if [ -f "$target_resource" ]; then
        size=$(stat -c%s "$target_resource" 2>/dev/null || echo "unknown")
        echo "  ✅ gulp构建文件: $(basename $target_resource) ($size bytes)"
      else
        echo "  ❌ 文件缺失: $(basename $target_resource)"
      fi
    done
  else
    echo "  ❌ 远程显示扩展在gulp构建中缺失"
    echo "  请检查扩展是否正确包含在gulp构建过程中"
    exit 1
  fi

  echo "✓ 从gulp构建复制应用结构完成（包含完整扩展）"
else
  echo "BUILD_DIR 不存在，手动构建..."

  # 寻找 Electron 分发包
  ELECTRON_DIRS=(
    "node_modules/electron/dist"
    "node_modules/.bin/electron"
    "/usr/share/electron"
  )

  ELECTRON_FOUND=false
  for electron_dir in "${ELECTRON_DIRS[@]}"; do
    if [ -d "$electron_dir" ]; then
      echo "使用 Electron 目录: $electron_dir"
      cp -r "$electron_dir/"* "$APP_DIR/"

      if [ -f "$APP_DIR/electron" ]; then
        mv "$APP_DIR/electron" "$APP_DIR/kylinrobot-ide"
      fi

      ELECTRON_FOUND=true
      break
    fi
  done

  if [ "$ELECTRON_FOUND" = false ]; then
    echo "错误: 未找到 Electron 分发包"
    exit 1
  fi

  # 创建 resources 目录结构
  mkdir -p "$APP_DIR/resources/app"

  # 复制编译后的代码
  echo "复制编译输出..."
  cp -r out/ "$APP_DIR/resources/app/"

  # 使用gulp构建的完整node_modules
  echo "使用gulp构建的完整node_modules..."
  cp -r "$BUILD_NODE_MODULES" "$APP_DIR/resources/app/node_modules"

  # 复制扩展（特别关注远程显示扩展）
  if [ -d "extensions" ]; then
    echo "复制扩展..."
    cp -r extensions/ "$APP_DIR/resources/app/"

    # 验证远程显示扩展复制完整性
    REMOTE_DISPLAY_TARGET="$APP_DIR/resources/app/extensions/remote-display"
    if [ -d "$REMOTE_DISPLAY_TARGET" ]; then
      echo "✓ 远程显示扩展已复制到: $REMOTE_DISPLAY_TARGET"

      # 验证关键文件
      for resource in "${REMOTE_DISPLAY_RESOURCES[@]}"; do
        target_resource="${resource/extensions\/remote-display/$REMOTE_DISPLAY_TARGET}"
        if [ -f "$target_resource" ]; then
          echo "✓ 远程显示资源已复制: $(basename $target_resource)"
        else
          echo "❌ 远程显示资源复制失败: $(basename $target_resource)"
        fi
      done
    else
      echo "❌ 远程显示扩展复制失败"
      exit 1
    fi
  fi

  echo "复制配置文件..."
  cp package.json "$APP_DIR/resources/app/"
  cp product.json "$APP_DIR/resources/app/"

  # 复制其他必要文件
  for file in LICENSE.txt ThirdPartyNotices.txt; do
    if [ -f "$file" ]; then
      cp "$file" "$APP_DIR/resources/app/"
    fi
  done

  if [ -d "resources" ]; then
    echo "复制资源文件..."
    cp -r resources/ "$APP_DIR/resources/app/"
  fi

  if [ ! -f "$APP_DIR/resources/app/extensions.json" ]; then
    echo "[]" > "$APP_DIR/resources/app/extensions.json"
  fi
fi

# 4. 精确权限设置（增强版）
echo "=== 精确权限设置（包含远程显示组件） ==="
chmod +x "$APP_DIR/kylinrobot-ide"

# 🔥 全面修复node_modules权限问题
echo "🔧 修复node_modules权限..."
if [ -d "$APP_DIR/resources/app/node_modules" ]; then
  # 设置所有目录为755 (drwxr-xr-x)，确保普通用户可以读取和执行
  find "$APP_DIR/resources/app/node_modules" -type d -exec chmod 755 {} + 2>/dev/null || true

  # 设置所有普通文件为644 (rw-r--r--)，确保普通用户可以读取
  find "$APP_DIR/resources/app/node_modules" -type f -exec chmod 644 {} + 2>/dev/null || true

  # 设置所有.node文件为755 (rwxr-xr-x)，确保普通用户可以执行
  find "$APP_DIR/resources/app/node_modules" -name "*.node" -type f -exec chmod 755 {} + 2>/dev/null || true

  # 设置其他可执行文件为755
  find "$APP_DIR/resources/app/node_modules" -name "*.sh" -type f -exec chmod 755 {} + 2>/dev/null || true
  find "$APP_DIR/resources/app/node_modules" -name "*.py" -type f -exec chmod 755 {} + 2>/dev/null || true

  echo "  ✅ node_modules权限已修复 (目录:755, 文件:644, 可执行文件:755)"
fi

# 修复extensions目录权限
if [ -d "$APP_DIR/resources/app/extensions" ]; then
  echo "🔧 修复extensions权限..."
  find "$APP_DIR/resources/app/extensions" -type d -exec chmod 755 {} + 2>/dev/null || true
  find "$APP_DIR/resources/app/extensions" -type f -exec chmod 644 {} + 2>/dev/null || true
  find "$APP_DIR/resources/app/extensions" -name "*.sh" -type f -exec chmod 755 {} + 2>/dev/null || true
  find "$APP_DIR/resources/app/extensions" -name "*.py" -type f -exec chmod 755 {} + 2>/dev/null || true
  echo "  ✅ extensions权限已修复"
fi

# 处理scripts（包含远程显示相关脚本）
if [ -d "scripts" ]; then
  mkdir -p "$APP_DIR/resources/app/scripts"
  cp -r "scripts/." "$APP_DIR/resources/app/scripts/"
  find "$APP_DIR/resources/app/scripts" -type d -exec chmod 755 {} + 2>/dev/null || true
  find "$APP_DIR/resources/app/scripts" -type f -exec chmod 644 {} + 2>/dev/null || true
  find "$APP_DIR/resources/app/scripts" -name "*.py" -type f -exec chmod 755 {} + 2>/dev/null || true
  find "$APP_DIR/resources/app/scripts" -name "*.sh" -type f -exec chmod 755 {} + 2>/dev/null || true
fi

# 确保主程序目录权限正确
find "$APP_DIR/resources/app" -type d -exec chmod 755 {} + 2>/dev/null || true
find "$APP_DIR/resources/app" -maxdepth 1 -type f -exec chmod 644 {} + 2>/dev/null || true

echo "✓ 权限设置完成（包含远程显示组件）"

# 5. 统计最终效果和对齐度（增强版）
final_file_count=$(find "$APP_DIR" -type f | wc -l)
final_node_count=$(find "$APP_DIR/resources/app/node_modules" -name "*.node" -type f | wc -l)
final_node_modules_count=$(find "$APP_DIR/resources/app/node_modules" -type f | wc -l)
remote_display_files=$(find "$APP_DIR/resources/app/extensions/remote-display" -type f | wc -l)

echo "✓ 最终应用统计："
echo "  总文件数: $final_file_count (官方目标: 2,952 + 远程显示组件)"
echo "  node_modules文件数: $final_node_modules_count"
echo "  .node文件数: $final_node_count"
echo "  远程显示扩展文件数: $remote_display_files"

# 6. 验证应用结构（增强版）
echo "=== 验证应用结构（包含远程显示功能） ==="
REQUIRED_FILES=(
  "$APP_DIR/kylinrobot-ide"
  "$APP_DIR/resources/app/package.json"
  "$APP_DIR/resources/app/product.json"
  "$APP_DIR/resources/app/out"
  "$APP_DIR/resources/app/node_modules"
  "$APP_DIR/resources/app/extensions/remote-display"
  "$APP_DIR/resources/app/extensions/remote-display/out/extension.js"
  "$APP_DIR/resources/app/extensions/remote-display/resources/noVNC-client"
)

for file in "${REQUIRED_FILES[@]}"; do
  if [ -e "$file" ]; then
    echo "✓ $file"
  else
    echo "❌ $file 缺失"
    exit 1
  fi
done

echo "✓ 应用结构验证通过（远程显示功能完整）"

# 7. 创建 DEB 包
echo "=== 创建 DEB 包 ==="
DEB_TEMP_DIR=".build/linux/deb/$DEB_ARCH/kylinrobot-ide-$DEB_ARCH"
DEB_OUTPUT_DIR="./dist"
DEB_FILE="$DEB_OUTPUT_DIR/kylinrobot-ide_${PACKAGE_VERSION}-${BUILD_TIMESTAMP}_${DEB_ARCH}.deb"

# 清理并创建目录
rm -rf "$DEB_TEMP_DIR"
mkdir -p "$DEB_TEMP_DIR"/{DEBIAN,usr/share/kylinrobot-ide,usr/bin}
mkdir -p "$DEB_OUTPUT_DIR"

# 复制应用到 DEB 包结构
echo "复制应用到 DEB 包结构..."
cp -r "$APP_DIR/"* "$DEB_TEMP_DIR/usr/share/kylinrobot-ide/"

# 🔥 处理 KylinRobot-v2 内置模块（新增功能）
echo "=== 处理 KylinRobot-v2 内置模块 ==="
KYLINROBOT_TAR_SOURCE="KylinRobot-v2.tar.gz"
KYLINROBOT_READY=false

if [ -f "$KYLINROBOT_TAR_SOURCE" ]; then
  echo "📦 准备 KylinRobot-v2 内置模块..."
  echo "  源压缩包: $KYLINROBOT_TAR_SOURCE"
  echo "  压缩包大小: $(du -h "$KYLINROBOT_TAR_SOURCE" | cut -f1)"

  # 验证压缩包完整性
  if tar -tzf "$KYLINROBOT_TAR_SOURCE" >/dev/null 2>&1; then
    echo "  ✅ 压缩包完整性验证通过"

    # 注意：这里不能直接复制，需要在创建DEB包结构后再复制
    # 标记需要复制KylinRobot-v2压缩包
    KYLINROBOT_READY=true

    echo "  ✅ KylinRobot-v2 内置模块已准备完成"
    echo "     - 源文件: $KYLINROBOT_TAR_SOURCE"
    echo "     - 目标位置: /opt/KylinRobot-v2 (安装时解压)"
    echo "     - 包内路径: /usr/share/kylinrobot-ide/kylinrobot-v2.tar.gz"
  else
    echo "  ❌ 错误: 压缩包损坏或格式不正确"
    echo "  跳过 KylinRobot-v2 内置模块处理"
  fi
elif [ -d "KylinRobot-v2" ]; then
    echo "📁 发现 KylinRobot-v2 目录，复制到 /opt 目录..."
    mkdir -p "$DEB_TEMP_DIR/opt"
    cp -r KylinRobot-v2/ "$DEB_TEMP_DIR/opt/"
    echo "✓ KylinRobot-v2 内置模块已复制到 /opt 目录"

    # 设置正确的权限
    find "$DEB_TEMP_DIR/opt/KylinRobot-v2" -type d -exec chmod 755 {} + 2>/dev/null || true
    find "$DEB_TEMP_DIR/opt/KylinRobot-v2" -type f -exec chmod 644 {} + 2>/dev/null || true
    find "$DEB_TEMP_DIR/opt/KylinRobot-v2" -name "*.py" -type f -exec chmod 755 {} + 2>/dev/null || true
    find "$DEB_TEMP_DIR/opt/KylinRobot-v2" -name "*.sh" -type f -exec chmod 755 {} + 2>/dev/null || true
    echo "✓ KylinRobot-v2 权限设置完成"

    # 统计内置模块文件
    kylinrobot_files=$(find "$DEB_TEMP_DIR/opt/KylinRobot-v2" -type f | wc -l)
    echo "✓ KylinRobot-v2 内置模块文件数: $kylinrobot_files"
else
  echo "⚠️ 警告: 未找到 KylinRobot-v2.tar.gz 或 KylinRobot-v2 目录，跳过内置模块处理"
  echo "   如需包含内置模块，请确保 KylinRobot-v2.tar.gz 存在于项目根目录"
fi

# 复制 KylinRobot-v2 压缩包到DEB包中（如果准备好了）
if [ "$KYLINROBOT_READY" = true ] && [ -f "$KYLINROBOT_TAR_SOURCE" ]; then
  echo "📦 复制 KylinRobot-v2 压缩包到DEB包..."
  cp "$KYLINROBOT_TAR_SOURCE" "$DEB_TEMP_DIR/usr/share/kylinrobot-ide/kylinrobot-v2.tar.gz"
  echo "  ✅ KylinRobot-v2 压缩包已复制到DEB包结构"
fi

# 8-12. 图标、启动脚本、控制文件等
echo "=== 处理图标和脚本 ==="
ICON_BASE_DIR="$DEB_TEMP_DIR/usr/share"
mkdir -p "$ICON_BASE_DIR/pixmaps"

if [ -f "resources/linux/kylinrobot-ide.png" ]; then
  cp "resources/linux/kylinrobot-ide.png" "$ICON_BASE_DIR/pixmaps/kylinrobot-ide.png"
fi

# 创建启动脚本
cat > "$DEB_TEMP_DIR/usr/bin/kylinrobot-ide" << 'EOF'
#!/bin/bash
# KylinRobot IDE 启动脚本

# 检查websockify是否可用
if ! command -v websockify >/dev/null 2>&1; then
    echo "警告: websockify未安装，远程显示功能可能无法正常工作"
    echo "请运行: pip3 install websockify"
fi

# 启动主程序
exec /usr/share/kylinrobot-ide/kylinrobot-ide "$@"
EOF
chmod 755 "$DEB_TEMP_DIR/usr/bin/kylinrobot-ide"

# 创建控制文件（增加远程显示依赖）
cat > "$DEB_TEMP_DIR/DEBIAN/control" << EOF
Package: kylinrobot-ide
Version: $PACKAGE_VERSION-$BUILD_TIMESTAMP
Section: devel
Architecture: $DEB_ARCH
Depends: python3-tk, python3-pil.imagetk, python3-pip, websockify
Recommends: tightvncserver | tigervnc-standalone-server | x11vnc
Maintainer: KylinRobot Team <<EMAIL>>
Description: KylinRobot-v2 定制版VSCode with Remote Display
 A customized version of VSCode for KylinRobot-v2 with Remote Display
 .
 此版本基于官方VS Code包结构构建，完整支持远程显示功能，
 包含VNC客户端、WebSocket代理和截图功能，适用于远程桌面自动化场景。
 .
 Features:
  - 完整的远程桌面显示功能
  - 基于noVNC的Web VNC客户端
  - WebSocket代理支持
  - 实时截图功能
  - 多VNC服务器支持
Priority: optional
Homepage: https://kylinrobot.com
EOF

# 安装脚本
cat > "$DEB_TEMP_DIR/DEBIAN/postinst" << 'EOF'
#!/bin/bash
set -e

echo "🔧 配置 KylinRobot IDE..."

# 创建桌面图标
cat > /usr/share/applications/kylinrobot-ide.desktop << 'DESKTOP_EOF'
[Desktop Entry]
Name=KylinRobot IDE
Comment=KylinRobot GUI Automation IDE with Remote Display Support
GenericName=Text Editor
Exec=/usr/share/kylinrobot-ide/kylinrobot-ide --unity-launch %F
Icon=kylinrobot-ide
Type=Application
StartupNotify=true
StartupWMClass=kylinrobot-ide
Categories=TextEditor;Development;IDE;RemoteAccess;
MimeType=text/plain;
Actions=new-empty-window;
Keywords=kylinrobot;ide;automation;test;vnc;remote;display;

[Desktop Action new-empty-window]
Name=New Empty Window
Exec=/usr/share/kylinrobot-ide/kylinrobot-ide --new-window %F
Icon=kylinrobot-ide
DESKTOP_EOF

chmod 644 /usr/share/applications/kylinrobot-ide.desktop

# 📦 安装 KylinRobot-v2 内置模块到 /opt
echo "📦 安装 KylinRobot-v2 内置模块..."
KYLINROBOT_TAR="/usr/share/kylinrobot-ide/kylinrobot-v2.tar.gz"
if [ -f "$KYLINROBOT_TAR" ]; then
    echo "  检查压缩包完整性..."
    if tar -tzf "$KYLINROBOT_TAR" >/dev/null 2>&1; then
        echo "  ✅ 压缩包完整性验证通过"

        # 清理现有目录（如果存在）
        if [ -d "/opt/KylinRobot-v2" ]; then
            echo "  清理现有 KylinRobot-v2 目录..."
            rm -rf /opt/KylinRobot-v2
        fi

        # 确保目标目录存在
        mkdir -p /opt

        # 解压到 /opt 目录
        echo "  解压 KylinRobot-v2 到 /opt 目录..."
        cd /opt
        tar -xzf "$KYLINROBOT_TAR"

        # 设置正确的权限
        if [ -d "/opt/KylinRobot-v2" ]; then
            echo "  设置 /opt/KylinRobot-v2 权限..."

            # 获取当前安装用户（如果是通过sudo安装，获取真实用户）
            INSTALL_USER="\${SUDO_USER:-\$(whoami)}"
            INSTALL_GROUP="\$(id -gn \$INSTALL_USER 2>/dev/null || echo 'users')"

            echo "  设置所有者为: \$INSTALL_USER:\$INSTALL_GROUP"

            # 设置所有权为安装用户，而不是root
            chown -R "\$INSTALL_USER:\$INSTALL_GROUP" /opt/KylinRobot-v2 2>/dev/null || {
                echo "  ⚠️  无法设置用户所有权，使用宽松权限模式"
                # 如果无法设置用户所有权，至少确保所有用户都可以读写
                chmod -R 755 /opt/KylinRobot-v2 2>/dev/null || true
            }

            # 设置目录和文件权限
            find /opt/KylinRobot-v2 -type d -exec chmod 755 {} + 2>/dev/null || true
            find /opt/KylinRobot-v2 -type f -exec chmod 644 {} + 2>/dev/null || true
            find /opt/KylinRobot-v2 -name "*.py" -exec chmod 755 {} + 2>/dev/null || true
            find /opt/KylinRobot-v2 -name "*.sh" -exec chmod 755 {} + 2>/dev/null || true

            echo "  ✅ KylinRobot-v2 内置模块安装完成: /opt/KylinRobot-v2"
            echo "  📋 所有者: \$(ls -ld /opt/KylinRobot-v2 | awk '{print \$3\":\"\$4}')"
        else
            echo "  ❌ 警告: KylinRobot-v2 解压失败"
        fi

        # 清理临时文件
        rm -f "$KYLINROBOT_TAR"
    else
        echo "  ❌ 警告: 压缩包损坏，跳过 KylinRobot-v2 安装"
    fi
else
    echo "  ⚠️  警告: 未找到 KylinRobot-v2 压缩包，跳过内置模块安装"
fi

# 检查并安装websockify（如果不存在）
echo "🔧 检查远程显示依赖..."
if ! command -v websockify >/dev/null 2>&1; then
    echo "正在安装websockify..."
    if command -v pip3 >/dev/null 2>&1; then
        pip3 install websockify || echo "警告: websockify安装失败，请手动安装"
    else
        echo "警告: pip3不可用，请手动安装websockify"
    fi
fi

# 创建远程显示功能快速配置脚本
cat > /usr/local/bin/kylinrobot-vnc-setup << 'VNC_SETUP_EOF'
#!/bin/bash
# KylinRobot IDE 远程显示功能快速配置脚本

echo "=== KylinRobot IDE 远程显示功能配置 ==="

# 检查VNC服务器
if ! command -v vncserver >/dev/null 2>&1 && ! command -v x11vnc >/dev/null 2>&1; then
    echo "未检测到VNC服务器，建议安装："
    echo "  Ubuntu/Debian: sudo apt install tightvncserver"
    echo "  CentOS/RHEL: sudo yum install tigervnc-server"
    echo "  或使用x11vnc: sudo apt install x11vnc"
    exit 1
fi

# 检查websockify
if ! command -v websockify >/dev/null 2>&1; then
    echo "websockify未安装，请运行: pip3 install websockify"
    exit 1
fi

echo "✓ 远程显示功能依赖检查通过"
echo ""
echo "快速启动VNC服务器和代理："
echo "  1. 启动VNC: vncserver :0"
echo "  2. 启动代理: websockify 6080 localhost:5900"
echo "  3. 打开KylinRobot IDE并使用远程显示功能"
echo ""
echo "详细配置请参考: /usr/share/doc/kylinrobot-ide/remote-display-setup.md"
VNC_SETUP_EOF

chmod +x /usr/local/bin/kylinrobot-vnc-setup

# 创建远程显示功能文档
mkdir -p /usr/share/doc/kylinrobot-ide
cat > /usr/share/doc/kylinrobot-ide/remote-display-setup.md << 'DOC_EOF'
# KylinRobot IDE 远程显示功能设置指南

## 快速开始

1. 安装VNC服务器：
   ```bash
   sudo apt install tightvncserver  # Ubuntu/Debian
   ```

2. 启动VNC服务器：
   ```bash
   vncserver :0
   ```

3. 启动WebSocket代理：
   ```bash
   websockify 6080 localhost:5900
   ```

4. 在KylinRobot IDE中使用远程显示功能

## 详细配置

请访问: https://kylinrobot.com/docs/remote-display
DOC_EOF

# 更新系统
if hash update-desktop-database 2>/dev/null; then
    update-desktop-database
fi

echo "KylinRobot IDE 安装完成"
echo "使用 'kylinrobot-vnc-setup' 命令配置远程显示功能"
exit 0
EOF

chmod 755 "$DEB_TEMP_DIR/DEBIAN/postinst"

# 创建卸载脚本（增强版）
cat > "$DEB_TEMP_DIR/DEBIAN/postrm" << 'EOF'
#!/bin/bash
set -e

if [ "$1" = "remove" ] || [ "$1" = "purge" ]; then
    echo "🧹 清理 KylinRobot IDE..."

    # 清理桌面文件
    rm -f /usr/share/applications/kylinrobot-ide.desktop
    rm -f /usr/local/bin/kylinrobot-vnc-setup
    rm -rf /usr/share/doc/kylinrobot-ide

    # 清理 KylinRobot-v2 内置模块
    echo "  清理 KylinRobot-v2 内置模块..."
    if [ -d "/opt/KylinRobot-v2" ]; then
        rm -rf /opt/KylinRobot-v2
        echo "  ✅ KylinRobot-v2 内置模块已卸载"
    fi

    if hash update-desktop-database 2>/dev/null; then
        update-desktop-database
    fi

    echo "  ✅ KylinRobot IDE 已卸载"
fi

exit 0
EOF

chmod 755 "$DEB_TEMP_DIR/DEBIAN/postrm"

# 13. 构建 DEB 包
echo "构建 DEB 包: $DEB_FILE"

# 确保输出目录存在
mkdir -p "$(dirname "$DEB_FILE")"

# 获取绝对路径
DEB_FILE_ABS=$(realpath "$DEB_FILE")
DEB_TEMP_DIR_ABS=$(realpath "$DEB_TEMP_DIR")

cd "$(dirname "$DEB_TEMP_DIR_ABS")"
if fakeroot dpkg-deb -Zxz -b "$(basename "$DEB_TEMP_DIR_ABS")" "$DEB_FILE_ABS"; then
  echo "✅ DEB 包构建成功: $DEB_FILE_ABS"
  DEB_FILE="$DEB_FILE_ABS"  # 更新变量为绝对路径
else
  echo "❌ DEB 包构建失败"
  exit 1
fi

# 14. 最终验证和功能对比
echo "=== 最终验证和功能对比 ==="
if [ -f "$DEB_FILE" ]; then
  DEB_SIZE=$(du -h "$DEB_FILE" | cut -f1)
  final_deb_files=$(dpkg --contents "$DEB_FILE" | wc -l)
  deb_node_files=$(dpkg --contents "$DEB_FILE" | grep -c "\.node$" || echo 0)
  deb_remote_display_files=$(dpkg --contents "$DEB_FILE" | grep -c "remote-display" || echo 0)

  echo "✓ DEB 包: $DEB_FILE ($DEB_SIZE)"
  echo "✓ DEB包文件数: $final_deb_files"
  echo "✓ DEB包.node文件数: $deb_node_files"
  echo "✓ DEB包远程显示文件数: $deb_remote_display_files"

  # 详细验证DEB包中的noVNC文件
  echo ""
  echo "🔍 验证DEB包中的noVNC资源:"

  # 检查关键noVNC文件在DEB包中的存在性
  DEB_NOVNC_CRITICAL_FILES=(
    "usr/share/kylinrobot-ide/resources/app/extensions/remote-display/resources/noVNC-client/core/rfb.js"
    "usr/share/kylinrobot-ide/resources/app/extensions/remote-display/out/extension.js"
    "usr/share/kylinrobot-ide/resources/app/extensions/remote-display/out/noVNCEmbeddedPanel.js"
    "usr/share/kylinrobot-ide/resources/app/extensions/remote-display/package.json"
  )

  missing_in_deb=0
  for deb_file in "${DEB_NOVNC_CRITICAL_FILES[@]}"; do
    if dpkg --contents "$DEB_FILE" | grep -q "$deb_file"; then
      echo "  ✅ $deb_file"
    else
      echo "  ❌ $deb_file"
      missing_in_deb=$((missing_in_deb + 1))
    fi
  done

  if [ $missing_in_deb -gt 0 ]; then
    echo "  ⚠️  DEB包中缺少 $missing_in_deb 个关键文件"
  else
    echo "  ✅ DEB包中包含所有关键noVNC文件"
  fi

  # 统计noVNC相关文件数量
  deb_novnc_files=$(dpkg --contents "$DEB_FILE" | grep -i "novnc\|rfb\.js" | wc -l)
  echo "  📊 DEB包中noVNC相关文件数: $deb_novnc_files"

  # 检查扩展的package.json是否正确包含
  echo ""
  echo "🔍 验证远程显示扩展配置:"
  if dpkg --contents "$DEB_FILE" | grep -q "remote-display/package.json"; then
    echo "  ✅ 扩展配置文件已包含"
  else
    echo "  ❌ 扩展配置文件缺失"
  fi

  # 检查 KylinRobot-v2 内置模块
  echo ""
  echo "🔍 验证 KylinRobot-v2 内置模块:"
  if dpkg --contents "$DEB_FILE" | grep -q "usr/share/kylinrobot-ide/kylinrobot-v2.tar.gz"; then
    kylinrobot_size=$(dpkg --contents "$DEB_FILE" | grep "kylinrobot-v2.tar.gz" | awk '{print $3}')
    echo "  ✅ KylinRobot-v2 内置模块: kylinrobot-v2.tar.gz (${kylinrobot_size} bytes)"
  elif dpkg --contents "$DEB_FILE" | grep -q "opt/KylinRobot-v2"; then
    kylinrobot_files=$(dpkg --contents "$DEB_FILE" | grep -c "opt/KylinRobot-v2" || echo 0)
    echo "  ✅ KylinRobot-v2 内置模块: 直接目录形式 (${kylinrobot_files} 个文件)"
  else
    echo "  ❌ KylinRobot-v2 内置模块缺失"
  fi

  # 🔥 版本一致性验证（关键步骤）
  echo ""
  echo "🔥 验证版本一致性（开发环境 vs DEB包）:"

  # 提取DEB包进行版本对比
  TEMP_VERSION_CHECK="./temp_version_check"
  rm -rf "$TEMP_VERSION_CHECK"
  mkdir -p "$TEMP_VERSION_CHECK"

  dpkg-deb -x "$DEB_FILE" "$TEMP_VERSION_CHECK" 2>/dev/null

  DEB_EXT_PATH="$TEMP_VERSION_CHECK/usr/share/kylinrobot-ide/resources/app/extensions/remote-display"
  DEV_EXT_PATH="extensions/remote-display"

  # 对比关键文件大小
  VERSION_ISSUES=0

  for check_file in "out/extension.js" "out/noVNCEmbeddedPanel.js" "out/websocketVNCBridge.js"; do
    if [ -f "$DEV_EXT_PATH/$check_file" ] && [ -f "$DEB_EXT_PATH/$check_file" ]; then
      dev_size=$(stat -c%s "$DEV_EXT_PATH/$check_file")
      deb_size=$(stat -c%s "$DEB_EXT_PATH/$check_file")

      if [ $dev_size -eq $deb_size ]; then
        echo "  ✅ $check_file: 版本一致 ($dev_size bytes)"
      else
        echo "  ❌ $check_file: 版本不一致 (开发: $dev_size bytes, DEB: $deb_size bytes)"
        VERSION_ISSUES=$((VERSION_ISSUES + 1))
      fi
    else
      echo "  ⚠️  $check_file: 文件缺失，无法对比"
      VERSION_ISSUES=$((VERSION_ISSUES + 1))
    fi
  done

  # 清理临时目录
  rm -rf "$TEMP_VERSION_CHECK"

  if [ $VERSION_ISSUES -eq 0 ]; then
    echo "  🎉 版本一致性验证通过！开发环境与DEB包完全匹配"
  else
    echo "  ⚠️  发现 $VERSION_ISSUES 个版本不一致问题"
    echo "  💡 这可能导致功能差异，建议重新检查编译过程"
  fi

  echo ""
  echo "📊 功能对比:"
  echo "  官方VS Code: 基础IDE功能"
  echo "  官方对齐版: 基础IDE + 稳定性优化"
  echo "  KylinRobot IDE: 基础IDE + 稳定性优化 + 完整远程显示功能"

  echo ""
  echo "🎯 KylinRobot IDE特性:"
  echo "  ✅ 严格按照官方VS Code包结构"
  echo "  ✅ 包含所有远程显示功能依赖"
  echo "  ✅ 完整的noVNC客户端支持"
  echo "  ✅ WebSocket代理自动配置"
  echo "  ✅ 多VNC服务器连接支持"
  echo "  ✅ 实时截图功能"
  echo "  ✅ 自动依赖检查和安装"
  echo "  ✅ 快速配置脚本"

else
  echo "❌ DEB 包文件不存在"
  exit 1
fi

echo "=== 清理临时文件 ==="
rm -rf "$APP_DIR"

echo ""
echo "🎯 KylinRobot IDE构建完成！"
echo "DEB 包: $DEB_FILE"
echo ""
echo "🏆 KylinRobot IDE特性总结:"
echo "  ✅ 基于官方VS Code包结构，确保稳定性"
echo "  ✅ 完整集成远程显示功能，开箱即用"
echo "  ✅ 自动安装websockify等必要依赖"
echo "  ✅ 提供快速配置脚本和详细文档"
echo "  ✅ 支持多种VNC服务器连接"
echo "  ✅ 包含实时截图和画面传输功能"
echo "  ✅ KylinRobot-v2 内置模块自动安装到 /opt/KylinRobot-v2"
echo ""
echo "📦 KylinRobot-v2 内置模块特性:"
echo "  🎯 自动安装到 /opt/KylinRobot-v2"
echo "  🔐 正确的权限设置 (755/644)"
echo "  🧹 卸载时自动清理"
echo "  ✅ 完整性验证和错误处理"
echo ""
echo "📋 安装后配置步骤:"
echo "  1. 安装DEB包: sudo dpkg -i '$DEB_FILE'"
echo "  2. 运行配置脚本: kylinrobot-vnc-setup"
echo "  3. 按照提示配置VNC服务器"
echo "  4. 在KylinRobot IDE中使用远程显示功能"
echo "  5. KylinRobot-v2 内置模块将自动可用于 /opt/KylinRobot-v2"
echo ""
echo "🔧 验证命令:"
echo "  ./simple-install-monitor.sh '$DEB_FILE'"
echo "  ./test-functionality.sh"
echo ""
echo "这个版本包含完整的远程显示功能支持和 KylinRobot-v2 内置模块！"
